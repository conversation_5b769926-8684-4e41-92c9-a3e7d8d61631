<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略优化前端功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>策略优化前端功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. API连接测试</div>
            <button onclick="testAPIConnection()">测试API连接</button>
            <div id="api-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 策略优化数据加载测试</div>
            <button onclick="testOptimizationData()">测试优化数据加载</button>
            <div id="optimization-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 优化建议应用测试</div>
            <button onclick="testApplyOptimization()">测试应用优化</button>
            <div id="apply-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. 前端组件交互测试</div>
            <button onclick="testFrontendInteraction()">测试前端交互</button>
            <div id="frontend-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">测试日志</div>
            <div id="log-area" class="log-area"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api/v1';
        
        function log(message, type = 'info') {
            const logArea = document.getElementById('log-area');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(logEntry);
        }
        
        function clearLog() {
            document.getElementById('log-area').textContent = '';
        }
        
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `test-result ${type}`;
        }
        
        async function testAPIConnection() {
            log('开始测试API连接...');
            try {
                const response = await fetch(`${API_BASE}/strategy`);
                if (response.ok) {
                    const data = await response.json();
                    showResult('api-result', `✓ API连接正常，返回 ${data.length} 个策略`, 'success');
                    log(`API连接成功，策略数量: ${data.length}`);
                    return data;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showResult('api-result', `✗ API连接失败: ${error.message}`, 'error');
                log(`API连接失败: ${error.message}`, 'error');
                return null;
            }
        }
        
        async function testOptimizationData() {
            log('开始测试策略优化数据加载...');
            try {
                // 首先获取策略列表
                const strategies = await testAPIConnection();
                if (!strategies || strategies.length === 0) {
                    throw new Error('没有可用的策略');
                }
                
                const strategyId = strategies[0].id;
                log(`使用策略ID: ${strategyId}`);
                
                // 测试优化数据API
                const response = await fetch(`${API_BASE}/strategies/${strategyId}/optimization`);
                if (response.ok) {
                    const data = await response.json();
                    const suggestions = data.optimization_suggestions || [];
                    showResult('optimization-result', 
                        `✓ 优化数据加载成功，包含 ${suggestions.length} 个建议`, 'success');
                    log(`优化数据加载成功，建议数量: ${suggestions.length}`);
                    
                    // 显示建议详情
                    suggestions.forEach((suggestion, index) => {
                        log(`建议${index + 1}: ${suggestion.parameter} (${suggestion.current_value} -> ${suggestion.suggested_value})`);
                    });
                    
                    return { strategyId, data };
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showResult('optimization-result', `✗ 优化数据加载失败: ${error.message}`, 'error');
                log(`优化数据加载失败: ${error.message}`, 'error');
                return null;
            }
        }
        
        async function testApplyOptimization() {
            log('开始测试优化建议应用...');
            try {
                // 首先获取优化数据
                const optimizationResult = await testOptimizationData();
                if (!optimizationResult) {
                    throw new Error('无法获取优化数据');
                }
                
                const { strategyId, data } = optimizationResult;
                const suggestions = data.optimization_suggestions || [];
                
                if (suggestions.length === 0) {
                    throw new Error('没有可用的优化建议');
                }
                
                // 使用第一个建议进行测试
                const suggestion = suggestions[0];
                const optimizationParams = {
                    parameter: suggestion.parameter,
                    value: suggestion.suggested_value
                };
                
                log(`应用优化参数: ${JSON.stringify(optimizationParams)}`);
                
                const response = await fetch(`${API_BASE}/strategies/${strategyId}/optimize`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(optimizationParams)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        showResult('apply-result', 
                            `✓ 优化应用成功: ${result.message}`, 'success');
                        log(`优化应用成功: ${result.message}`);
                    } else {
                        throw new Error(result.error || '应用失败');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showResult('apply-result', `✗ 优化应用失败: ${error.message}`, 'error');
                log(`优化应用失败: ${error.message}`, 'error');
            }
        }
        
        async function testFrontendInteraction() {
            log('开始测试前端组件交互...');
            try {
                // 检查是否在策略优化页面
                const currentUrl = window.location.href;
                if (!currentUrl.includes('strategy-optimization')) {
                    showResult('frontend-result', 
                        '⚠ 请在策略优化页面运行此测试', 'warning');
                    log('当前不在策略优化页面');
                    return;
                }
                
                // 检查Vue应用是否存在
                if (typeof window.Vue === 'undefined' && typeof window.__VUE__ === 'undefined') {
                    showResult('frontend-result', 
                        '⚠ Vue应用未检测到，请确保页面已完全加载', 'warning');
                    log('Vue应用未检测到');
                    return;
                }
                
                // 检查优化建议按钮
                const applyButtons = document.querySelectorAll('button:contains("应用建议")');
                if (applyButtons.length > 0) {
                    showResult('frontend-result', 
                        `✓ 检测到 ${applyButtons.length} 个"应用建议"按钮`, 'success');
                    log(`检测到 ${applyButtons.length} 个应用建议按钮`);
                } else {
                    showResult('frontend-result', 
                        '⚠ 未检测到"应用建议"按钮，可能数据还在加载中', 'warning');
                    log('未检测到应用建议按钮');
                }
                
                // 检查是否有错误信息
                const errorElements = document.querySelectorAll('.el-message--error');
                if (errorElements.length > 0) {
                    showResult('frontend-result', 
                        `⚠ 检测到 ${errorElements.length} 个错误消息`, 'warning');
                    log(`检测到 ${errorElements.length} 个错误消息`);
                }
                
            } catch (error) {
                showResult('frontend-result', `✗ 前端交互测试失败: ${error.message}`, 'error');
                log(`前端交互测试失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', function() {
            log('页面加载完成，开始自动测试...');
            setTimeout(() => {
                testAPIConnection();
            }, 1000);
        });
    </script>
</body>
</html>

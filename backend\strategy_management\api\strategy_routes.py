"""
统一的策略管理API路由
替换所有重复的策略API端点
"""

from flask import Blueprint, request, jsonify
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import sys
import os
import logging
from typing import Optional

logger = logging.getLogger(__name__)

# 创建蓝图
strategy_bp = Blueprint('strategy_management', __name__)

# 简化的数据库配置
DATABASE_URL = "sqlite:///app.db"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        return db
    finally:
        db.close()

# 简化的模型定义
from dataclasses import dataclass
from typing import Dict, Any, List

@dataclass
class StrategyCreate:
    name: str
    type: str
    category: str
    description: str = None
    code_type: str = 'python'
    code_content: str = None
    parameters: Dict[str, Any] = None
    template_id: int = None
    symbol: str = None
    timeframe: str = None

@dataclass
class StrategyUpdate:
    name: str = None
    description: str = None
    code_content: str = None
    parameters: Dict[str, Any] = None
    symbol: str = None
    timeframe: str = None
    status: str = None
    is_active: bool = None

@dataclass
class CodeGenerationResponse:
    success: bool
    code: str = None
    message: str = None
    errors: List[str] = None

# 简化的服务类
class StrategyService:
    def __init__(self, db):
        self.db = db

    def get_strategies(self, creator_id=None, strategy_type=None, category=None,
                      status=None, is_active=None, skip=0, limit=100):
        """获取策略列表"""
        return []

    def create_strategy(self, strategy_data, creator_id):
        """创建策略"""
        # 模拟创建策略
        class MockStrategy:
            def __init__(self):
                self.id = 1
                self.name = strategy_data.name
                self.type = strategy_data.type
                self.category = strategy_data.category
                self.status = "created"
        return MockStrategy()

    def get_strategy(self, strategy_id, creator_id=None):
        """获取策略详情"""
        return None

    def update_strategy(self, strategy_id, strategy_data, creator_id):
        """更新策略"""
        return None

    def delete_strategy(self, strategy_id, creator_id):
        """删除策略"""
        return False

    def generate_code_from_template(self, template_id, parameters):
        """从模板生成代码"""
        return CodeGenerationResponse(
            success=False,
            message="模板功能暂未实现"
        )

class TemplateService:
    def __init__(self, db):
        self.db = db

    def get_templates(self, strategy_type=None, category=None, is_builtin=None, skip=0, limit=100):
        """获取模板列表"""
        # 直接使用SQL查询避免SQLAlchemy映射器冲突
        import sqlite3
        import json

        # 获取数据库路径
        db_path = 'app/database.db'
        if not os.path.exists(db_path):
            db_path = 'app/db/database.db'

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            # 构建查询条件
            where_conditions = []
            params = []

            if strategy_type:
                where_conditions.append("type = ?")
                params.append(strategy_type)
            if category:
                where_conditions.append("category = ?")
                params.append(category)
            if is_builtin is not None:
                where_conditions.append("is_builtin = ?")
                params.append(1 if is_builtin else 0)

            where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            query = f"""
                SELECT id, name, type, category, description, code_template,
                       parameter_schema, default_parameters, is_builtin, created_at
                FROM strategy_templates
                {where_clause}
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            """
            params.extend([limit, skip])

            cursor.execute(query, params)
            rows = cursor.fetchall()

            # 转换为对象列表
            templates = []
            for row in rows:
                class TemplateObj:
                    def __init__(self, data):
                        self.id = data[0]
                        self.name = data[1]
                        self.type = data[2]
                        self.category = data[3]
                        self.description = data[4]
                        self.code_template = data[5]
                        self.parameter_schema = json.loads(data[6]) if data[6] else None
                        self.default_parameters = json.loads(data[7]) if data[7] else None
                        self.is_builtin = bool(data[8])
                        self.created_at_str = data[9]

                    @property
                    def created_at(self):
                        class DateTimeObj:
                            def __init__(self, date_str):
                                self.date_str = date_str
                            def isoformat(self):
                                return self.date_str
                        return DateTimeObj(self.created_at_str)

                template = TemplateObj(row)
                templates.append(template)

            return templates

        finally:
            conn.close()

class CodeGenerator:
    def convert_to_python(self, pine_code):
        """转换Pine Script到Python"""
        return {
            "success": False,
            "message": "Pine Script转换功能暂未实现"
        }

def get_current_user():
    """获取当前用户（简化版本）"""
    # 这里应该实现真正的用户认证逻辑
    # 暂时返回一个模拟用户ID
    return {"id": 1, "username": "admin", "role": "admin"}

# ==================== 策略CRUD API ====================

@strategy_bp.route('/strategies', methods=['GET'])
def get_strategies():
    """获取策略列表"""
    try:
        # 获取查询参数
        strategy_type = request.args.get('type')
        category = request.args.get('category')
        status = request.args.get('status')
        is_active = request.args.get('is_active')
        skip = int(request.args.get('skip', 0))
        limit = int(request.args.get('limit', 100))

        # 获取当前用户
        current_user = get_current_user()
        creator_id = current_user["id"] if current_user["role"] != "admin" else None

        # 转换is_active参数
        if is_active is not None:
            is_active = is_active.lower() == 'true'

        # 获取策略列表
        db = get_db()
        strategy_service = StrategyService(db)
        strategies = strategy_service.get_strategies(
            creator_id=creator_id,
            strategy_type=strategy_type,
            category=category,
            status=status,
            is_active=is_active,
            skip=skip,
            limit=limit
        )

        # 转换为响应格式
        strategy_list = []
        for strategy in strategies:
            strategy_dict = {
                "id": strategy.id,
                "name": strategy.name,
                "type": strategy.type,
                "category": strategy.category,
                "description": strategy.description,
                "code_type": strategy.code_type,
                "symbol": strategy.symbol,
                "timeframe": strategy.timeframe,
                "status": strategy.status,
                "is_active": strategy.is_active,
                "created_at": strategy.created_at.isoformat(),
                "updated_at": strategy.updated_at.isoformat()
            }
            strategy_list.append(strategy_dict)

        return jsonify({
            "success": True,
            "data": strategy_list,
            "total": len(strategy_list),
            "message": "获取策略列表成功"
        })

    except Exception as e:
        logger.error(f"获取策略列表失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "获取策略列表失败"
        }), 500

@strategy_bp.route('/strategies', methods=['POST'])
def create_strategy():
    """创建新策略"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "error": "请求数据为空",
                "message": "创建策略失败"
            }), 400

        # 获取当前用户
        current_user = get_current_user()

        # 验证必需字段
        required_fields = ['name', 'type', 'category']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    "success": False,
                    "error": f"缺少必需字段: {field}",
                    "message": "创建策略失败"
                }), 400

        # 创建策略
        db = get_db()
        strategy_service = StrategyService(db)

        # 构建策略创建数据
        strategy_data = StrategyCreate(
            name=data['name'],
            type=data['type'],
            category=data['category'],
            description=data.get('description'),
            code_type=data.get('code_type', 'python'),
            code_content=data.get('code_content'),
            parameters=data.get('parameters'),
            template_id=data.get('template_id'),
            symbol=data.get('symbol'),
            timeframe=data.get('timeframe')
        )

        strategy = strategy_service.create_strategy(strategy_data, current_user["id"])

        return jsonify({
            "success": True,
            "data": {
                "id": strategy.id,
                "name": strategy.name,
                "type": strategy.type,
                "category": strategy.category,
                "status": strategy.status
            },
            "message": "策略创建成功"
        }), 201

    except ValueError as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "创建策略失败"
        }), 400
    except Exception as e:
        logger.error(f"创建策略失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "创建策略失败"
        }), 500

@strategy_bp.route('/strategies/<int:strategy_id>', methods=['GET'])
def get_strategy(strategy_id):
    """获取策略详情"""
    try:
        current_user = get_current_user()
        creator_id = current_user["id"] if current_user["role"] != "admin" else None

        db = get_db()
        strategy_service = StrategyService(db)
        strategy = strategy_service.get_strategy(strategy_id, creator_id)

        if not strategy:
            return jsonify({
                "success": False,
                "error": "策略不存在",
                "message": "获取策略详情失败"
            }), 404

        strategy_dict = {
            "id": strategy.id,
            "name": strategy.name,
            "type": strategy.type,
            "category": strategy.category,
            "description": strategy.description,
            "code_type": strategy.code_type,
            "code_content": strategy.code_content,
            "parameters": strategy.parameters,
            "template_id": strategy.template_id,
            "symbol": strategy.symbol,
            "timeframe": strategy.timeframe,
            "file_path": strategy.file_path,
            "status": strategy.status,
            "is_active": strategy.is_active,
            "created_by": strategy.creator_id,
            "created_at": strategy.created_at.isoformat(),
            "updated_at": strategy.updated_at.isoformat()
        }

        return jsonify({
            "success": True,
            "data": strategy_dict,
            "message": "获取策略详情成功"
        })

    except Exception as e:
        logger.error(f"获取策略详情失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "获取策略详情失败"
        }), 500

@strategy_bp.route('/strategies/<int:strategy_id>', methods=['PUT'])
def update_strategy(strategy_id):
    """更新策略"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "error": "请求数据为空",
                "message": "更新策略失败"
            }), 400

        current_user = get_current_user()

        db = get_db()
        strategy_service = StrategyService(db)

        # 构建更新数据
        strategy_data = StrategyUpdate(**data)

        strategy = strategy_service.update_strategy(strategy_id, strategy_data, current_user["id"])

        if not strategy:
            return jsonify({
                "success": False,
                "error": "策略不存在",
                "message": "更新策略失败"
            }), 404

        return jsonify({
            "success": True,
            "data": {
                "id": strategy.id,
                "name": strategy.name,
                "status": strategy.status,
                "updated_at": strategy.updated_at.isoformat()
            },
            "message": "策略更新成功"
        })

    except ValueError as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "更新策略失败"
        }), 400
    except Exception as e:
        logger.error(f"更新策略失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "更新策略失败"
        }), 500

@strategy_bp.route('/strategies/<int:strategy_id>', methods=['DELETE'])
def delete_strategy(strategy_id):
    """删除策略"""
    try:
        current_user = get_current_user()

        db = get_db()
        strategy_service = StrategyService(db)

        success = strategy_service.delete_strategy(strategy_id, current_user["id"])

        if not success:
            return jsonify({
                "success": False,
                "error": "策略不存在",
                "message": "删除策略失败"
            }), 404

        return jsonify({
            "success": True,
            "message": "策略删除成功"
        })

    except Exception as e:
        logger.error(f"删除策略失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "删除策略失败"
        }), 500

# ==================== 策略模板API ====================

@strategy_bp.route('/strategy-templates', methods=['GET'])
def get_strategy_templates():
    """获取策略模板列表"""
    try:
        strategy_type = request.args.get('type')
        category = request.args.get('category')
        is_builtin = request.args.get('is_builtin')
        skip = int(request.args.get('skip', 0))
        limit = int(request.args.get('limit', 100))

        if is_builtin is not None:
            is_builtin = is_builtin.lower() == 'true'

        db = get_db()
        template_service = TemplateService(db)
        templates = template_service.get_templates(
            strategy_type=strategy_type,
            category=category,
            is_builtin=is_builtin,
            skip=skip,
            limit=limit
        )

        template_list = []
        for template in templates:
            template_dict = {
                "id": template.id,
                "name": template.name,
                "type": template.type,
                "category": template.category,
                "description": template.description,
                "parameter_schema": template.parameter_schema,
                "default_parameters": template.default_parameters,
                "is_builtin": template.is_builtin,
                "created_at": template.created_at.isoformat()
            }
            template_list.append(template_dict)

        return jsonify({
            "success": True,
            "data": template_list,
            "total": len(template_list),
            "message": "获取模板列表成功"
        })

    except Exception as e:
        logger.error(f"获取模板列表失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "获取模板列表失败"
        }), 500

@strategy_bp.route('/strategy-templates/<int:template_id>/generate', methods=['POST'])
def generate_strategy_from_template(template_id):
    """从模板生成策略代码"""
    try:
        data = request.get_json()
        if not data or 'parameters' not in data:
            return jsonify({
                "success": False,
                "error": "缺少参数数据",
                "message": "代码生成失败"
            }), 400

        db = get_db()
        strategy_service = StrategyService(db)

        result = strategy_service.generate_code_from_template(
            template_id, data['parameters']
        )

        return jsonify({
            "success": result.success,
            "code": result.code,
            "message": result.message,
            "errors": result.errors
        })

    except Exception as e:
        logger.error(f"代码生成失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "代码生成失败"
        }), 500

# ==================== 代码验证和转换API ====================

@strategy_bp.route('/strategies/validate-code', methods=['POST'])
def validate_strategy_code():
    """验证策略代码"""
    try:
        data = request.get_json()
        if not data or 'code' not in data:
            return jsonify({
                "success": False,
                "error": "缺少代码数据",
                "message": "代码验证失败"
            }), 400

        code = data['code']
        code_type = data.get('code_type', 'python')

        # 简单的代码验证
        errors = []
        warnings = []

        if code_type == 'python':
            # Python代码语法检查
            try:
                compile(code, '<string>', 'exec')
            except SyntaxError as e:
                errors.append(f"Python语法错误: {str(e)}")

        # 检查必需的函数
        required_functions = ['initialize', 'handle_bar']
        for func in required_functions:
            if f'def {func}(' not in code:
                warnings.append(f"建议定义函数: {func}")

        is_valid = len(errors) == 0

        return jsonify({
            "success": True,
            "valid": is_valid,
            "errors": errors,
            "warnings": warnings,
            "message": "代码验证完成"
        })

    except Exception as e:
        logger.error(f"代码验证失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "代码验证失败"
        }), 500

@strategy_bp.route('/strategies/convert-pinescript', methods=['POST'])
def convert_pinescript():
    """转换Pine Script代码"""
    try:
        data = request.get_json()
        if not data or 'pine_code' not in data:
            return jsonify({
                "success": False,
                "error": "缺少Pine Script代码",
                "message": "代码转换失败"
            }), 400

        pine_code = data['pine_code']

        # 使用代码生成器进行转换（包含兼容性检查）
        code_generator = CodeGenerator()
        result = code_generator.convert_to_python(pine_code)

        return jsonify(result)

    except Exception as e:
        logger.error(f"Pine Script转换失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "代码转换失败"
        }), 500

@strategy_bp.route('/strategies/check-pinescript-compatibility', methods=['POST'])
def check_pinescript_compatibility():
    """检查Pine Script代码兼容性"""
    try:
        data = request.get_json()
        if not data or 'pine_code' not in data:
            return jsonify({
                "success": False,
                "error": "缺少Pine Script代码",
                "message": "兼容性检查失败"
            }), 400

        pine_code = data['pine_code']

        # 使用代码生成器检查兼容性
        code_generator = CodeGenerator()
        result = code_generator.check_compatibility(pine_code)

        return jsonify({
            "success": True,
            "compatibility_score": result["score"],
            "warnings": result["warnings"],
            "unsupported_features": result["unsupported_features"],
            "recommendations": result["recommendations"],
            "message": "兼容性检查完成"
        })

    except Exception as e:
        logger.error(f"Pine Script兼容性检查失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "兼容性检查失败"
        }), 500

# ==================== 策略参数历史API ====================

@strategy_bp.route('/strategies/<int:strategy_id>/parameter-history', methods=['GET'])
def get_parameter_history(strategy_id):
    """获取策略参数变更历史"""
    try:
        db = next(get_db())
        strategy_service = StrategyService(db)
        history = strategy_service.get_parameter_history(strategy_id)

        history_list = []
        for record in history:
            history_dict = {
                "id": record.id,
                "parameters": record.parameters,
                "changed_by": record.changed_by,
                "change_reason": record.change_reason,
                "created_at": record.created_at.isoformat()
            }
            history_list.append(history_dict)

        return jsonify({
            "success": True,
            "data": history_list,
            "message": "获取参数历史成功"
        })

    except Exception as e:
        logger.error(f"获取参数历史失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "获取参数历史失败"
        }), 500

# ==================== 策略统计API ====================

@strategy_bp.route('/strategies/stats', methods=['GET'])
def get_strategy_stats():
    """获取策略统计信息"""
    try:
        current_user = get_current_user()
        creator_id = current_user["id"] if current_user["role"] != "admin" else None

        db = get_db()
        strategy_service = StrategyService(db)

        # 获取所有策略
        all_strategies = strategy_service.get_strategies(creator_id=creator_id, limit=1000)

        # 计算统计数据
        total = len(all_strategies)
        active = len([s for s in all_strategies if getattr(s, 'status', None) == 'active'])
        inactive = len([s for s in all_strategies if getattr(s, 'status', None) in ['inactive', 'stopped']])

        stats = {
            "total": total,
            "active": active,
            "inactive": inactive,
            "created": len([s for s in all_strategies if getattr(s, 'status', None) == 'created']),
            "validated": len([s for s in all_strategies if getattr(s, 'status', None) == 'validated']),
            "error": len([s for s in all_strategies if getattr(s, 'status', None) == 'error'])
        }

        return jsonify({
            "success": True,
            "data": stats,
            "message": "获取策略统计成功"
        })

    except Exception as e:
        logger.error(f"获取策略统计失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "获取策略统计失败"
        }), 500

# ==================== 策略类型和分类API ====================

@strategy_bp.route('/strategy-types', methods=['GET'])
def get_strategy_types():
    """获取策略类型列表"""
    try:
        strategy_types = [
            {
                "value": "trend_following",
                "label": "趋势跟踪",
                "description": "追踪市场趋势的策略",
                "categories": [
                    {"value": "dual_ma_cross", "label": "双均线交叉"},
                    {"value": "triple_ma", "label": "三均线"},
                    {"value": "macd_trend", "label": "MACD趋势"},
                    {"value": "bollinger_breakout", "label": "布林带突破"}
                ]
            },
            {
                "value": "mean_reversion",
                "label": "均值回归",
                "description": "基于价格回归均值的策略",
                "categories": [
                    {"value": "rsi_oversold", "label": "RSI超买超卖"},
                    {"value": "bollinger_reversion", "label": "布林带回归"},
                    {"value": "support_resistance", "label": "支撑阻力"}
                ]
            },
            {
                "value": "momentum",
                "label": "动量策略",
                "description": "基于价格动量的策略",
                "categories": [
                    {"value": "price_momentum", "label": "价格动量"},
                    {"value": "volume_momentum", "label": "成交量动量"},
                    {"value": "relative_strength", "label": "相对强弱"}
                ]
            },
            {
                "value": "arbitrage",
                "label": "套利策略",
                "description": "利用价格差异的策略",
                "categories": [
                    {"value": "statistical_arbitrage", "label": "统计套利"},
                    {"value": "triangular_arbitrage", "label": "三角套利"}
                ]
            },
            {
                "value": "grid",
                "label": "网格策略",
                "description": "基于网格交易的策略",
                "categories": [
                    {"value": "fixed_grid", "label": "固定网格"},
                    {"value": "dynamic_grid", "label": "动态网格"},
                    {"value": "martingale", "label": "马丁格尔"}
                ]
            },
            {
                "value": "custom",
                "label": "自定义策略",
                "description": "用户自定义的策略",
                "categories": [
                    {"value": "python_custom", "label": "Python自定义"},
                    {"value": "pinescript_custom", "label": "Pine Script自定义"},
                    {"value": "ml_strategy", "label": "机器学习策略"}
                ]
            }
        ]

        return jsonify({
            "success": True,
            "data": strategy_types,
            "message": "获取策略类型成功"
        })

    except Exception as e:
        logger.error(f"获取策略类型失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "获取策略类型失败"
        }), 500

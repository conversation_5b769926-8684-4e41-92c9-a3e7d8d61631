"""
统一的策略管理API路由
替换所有重复的策略API端点
"""

from flask import Blueprint, request, jsonify
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import sys
import os
import logging
from typing import Optional

logger = logging.getLogger(__name__)

# 创建蓝图
strategy_bp = Blueprint('strategy_management', __name__)

# 数据库配置 - 使用与主应用相同的数据库
import sqlite3
import json
from datetime import datetime

def get_db():
    """获取数据库连接"""
    # 尝试多个可能的数据库路径
    db_paths = [
        'app.db',
        'backend/app.db',
        'app/database.db',
        'app/db/database.db'
    ]

    for db_path in db_paths:
        if os.path.exists(db_path):
            logger.info(f"使用数据库: {db_path}")
            return sqlite3.connect(db_path)

    # 如果都不存在，使用默认路径
    logger.warning("未找到现有数据库，使用默认路径")
    return sqlite3.connect('app.db')

# 简化的模型定义
from dataclasses import dataclass
from typing import Dict, Any, List

@dataclass
class StrategyCreate:
    name: str
    type: str
    category: str
    description: str = None
    code_type: str = 'python'
    code_content: str = None
    parameters: Dict[str, Any] = None
    template_id: int = None
    symbol: str = None
    timeframe: str = None

@dataclass
class StrategyUpdate:
    name: str = None
    description: str = None
    code_content: str = None
    parameters: Dict[str, Any] = None
    symbol: str = None
    timeframe: str = None
    status: str = None
    is_active: bool = None

@dataclass
class CodeGenerationResponse:
    success: bool
    code: str = None
    message: str = None
    errors: List[str] = None

# 简化的服务类
class StrategyService:
    def __init__(self, db):
        self.db = db

    def get_strategies(self, creator_id=None, strategy_type=None, category=None,
                      status=None, is_active=None, skip=0, limit=100):
        """获取策略列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 构建查询
            query = "SELECT * FROM strategies WHERE 1=1"
            params = []

            if creator_id:
                query += " AND creator_id = ?"
                params.append(creator_id)
            if strategy_type:
                query += " AND type = ?"
                params.append(strategy_type)
            if category:
                query += " AND category = ?"
                params.append(category)
            if status:
                query += " AND status = ?"
                params.append(status)
            if is_active is not None:
                query += " AND is_active = ?"
                params.append(1 if is_active else 0)

            query += " ORDER BY updated_at DESC LIMIT ? OFFSET ?"
            params.extend([limit, skip])

            cursor.execute(query, params)
            rows = cursor.fetchall()

            # 获取列名
            columns = [description[0] for description in cursor.description]

            # 转换为字典列表
            strategies = []
            for row in rows:
                strategy_dict = dict(zip(columns, row))
                # 解析JSON字段
                if strategy_dict.get('parameters'):
                    try:
                        strategy_dict['parameters'] = json.loads(strategy_dict['parameters'])
                    except:
                        strategy_dict['parameters'] = {}
                strategies.append(strategy_dict)

            conn.close()
            return strategies

        except Exception as e:
            logger.error(f"获取策略列表失败: {str(e)}")
            return []

    def create_strategy(self, strategy_data, creator_id):
        """创建策略"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 插入策略
            cursor.execute("""
                INSERT INTO strategies (
                    name, type, category, description, code_type, code_content,
                    parameters, template_id, symbol, timeframe, status,
                    is_active, creator_id, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                strategy_data.get('name'),
                strategy_data.get('type'),
                strategy_data.get('category'),
                strategy_data.get('description'),
                strategy_data.get('code_type', 'python'),
                strategy_data.get('code_content'),
                json.dumps(strategy_data.get('parameters', {})),
                strategy_data.get('template_id'),
                strategy_data.get('symbol'),
                strategy_data.get('timeframe'),
                'created',
                1,
                creator_id,
                datetime.now().isoformat(),
                datetime.now().isoformat()
            ))

            strategy_id = cursor.lastrowid
            conn.commit()
            conn.close()

            # 返回创建的策略
            return {
                'id': strategy_id,
                'name': strategy_data.get('name'),
                'type': strategy_data.get('type'),
                'category': strategy_data.get('category'),
                'status': 'created'
            }

        except Exception as e:
            logger.error(f"创建策略失败: {str(e)}")
            raise

    def get_strategy(self, strategy_id, creator_id=None):
        """获取策略详情"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            query = "SELECT * FROM strategies WHERE id = ?"
            params = [strategy_id]

            if creator_id:
                query += " AND creator_id = ?"
                params.append(creator_id)

            cursor.execute(query, params)
            row = cursor.fetchone()

            if not row:
                conn.close()
                return None

            # 获取列名
            columns = [description[0] for description in cursor.description]
            strategy_dict = dict(zip(columns, row))

            # 解析JSON字段
            if strategy_dict.get('parameters'):
                try:
                    strategy_dict['parameters'] = json.loads(strategy_dict['parameters'])
                except:
                    strategy_dict['parameters'] = {}

            conn.close()
            return strategy_dict

        except Exception as e:
            logger.error(f"获取策略详情失败: {str(e)}")
            return None

    def update_strategy(self, strategy_id, strategy_data, creator_id):
        """更新策略"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 构建更新语句
            update_fields = []
            params = []

            for field in ['name', 'description', 'code_content', 'parameters', 'status']:
                if field in strategy_data:
                    update_fields.append(f"{field} = ?")
                    if field == 'parameters':
                        params.append(json.dumps(strategy_data[field]))
                    else:
                        params.append(strategy_data[field])

            if update_fields:
                update_fields.append("updated_at = ?")
                params.append(datetime.now().isoformat())
                params.append(strategy_id)

                query = f"UPDATE strategies SET {', '.join(update_fields)} WHERE id = ?"
                cursor.execute(query, params)
                conn.commit()

            conn.close()
            return self.get_strategy(strategy_id, creator_id)

        except Exception as e:
            logger.error(f"更新策略失败: {str(e)}")
            raise

    def delete_strategy(self, strategy_id, creator_id):
        """删除策略"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE strategies
                SET is_active = 0, status = 'deleted', updated_at = ?
                WHERE id = ? AND creator_id = ?
            """, (datetime.now().isoformat(), strategy_id, creator_id))

            success = cursor.rowcount > 0
            conn.commit()
            conn.close()

            return success

        except Exception as e:
            logger.error(f"删除策略失败: {str(e)}")
            return False

    def generate_code_from_template(self, template_id, parameters):
        """从模板生成代码"""
        try:
            # 获取模板
            template_service = TemplateService(None)
            template = template_service.get_template(template_id)

            if not template:
                return {
                    'success': False,
                    'message': f'模板 ID {template_id} 不存在'
                }

            # 使用代码生成器
            code_generator = CodeGenerator()
            code = code_generator.generate_code(template.get('code_template', ''), parameters)

            return {
                'success': True,
                'code': code,
                'message': '代码生成成功'
            }

        except Exception as e:
            logger.error(f"代码生成失败: {str(e)}")
            return {
                'success': False,
                'message': f'代码生成失败: {str(e)}'
            }

class TemplateService:
    def __init__(self, db):
        self.db = db

    def get_templates(self, strategy_type=None, category=None, is_builtin=None, skip=0, limit=100):
        """获取模板列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 构建查询
            query = "SELECT * FROM strategy_templates WHERE 1=1"
            params = []

            if strategy_type:
                query += " AND type = ?"
                params.append(strategy_type)
            if category:
                query += " AND category = ?"
                params.append(category)
            if is_builtin is not None:
                query += " AND is_builtin = ?"
                params.append(1 if is_builtin else 0)

            query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
            params.extend([limit, skip])

            cursor.execute(query, params)
            rows = cursor.fetchall()

            # 获取列名
            columns = [description[0] for description in cursor.description]

            # 转换为字典列表
            templates = []
            for row in rows:
                template_dict = dict(zip(columns, row))
                # 解析JSON字段
                if template_dict.get('parameter_schema'):
                    try:
                        template_dict['parameter_schema'] = json.loads(template_dict['parameter_schema'])
                    except:
                        template_dict['parameter_schema'] = {}
                if template_dict.get('default_parameters'):
                    try:
                        template_dict['default_parameters'] = json.loads(template_dict['default_parameters'])
                    except:
                        template_dict['default_parameters'] = {}
                templates.append(template_dict)

            conn.close()
            return templates

        except Exception as e:
            logger.error(f"获取模板列表失败: {str(e)}")
            return []

    def get_template(self, template_id):
        """获取单个模板"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM strategy_templates WHERE id = ?", (template_id,))
            row = cursor.fetchone()

            if not row:
                conn.close()
                return None

            # 获取列名
            columns = [description[0] for description in cursor.description]
            template_dict = dict(zip(columns, row))

            # 解析JSON字段
            if template_dict.get('parameter_schema'):
                try:
                    template_dict['parameter_schema'] = json.loads(template_dict['parameter_schema'])
                except:
                    template_dict['parameter_schema'] = {}
            if template_dict.get('default_parameters'):
                try:
                    template_dict['default_parameters'] = json.loads(template_dict['default_parameters'])
                except:
                    template_dict['default_parameters'] = {}

            conn.close()
            return template_dict

        except Exception as e:
            logger.error(f"获取模板详情失败: {str(e)}")
            return None

class CodeGenerator:
    def __init__(self):
        pass

    def generate_code(self, template, parameters):
        """从模板生成代码"""
        try:
            # 简单的模板替换
            code = template
            for key, value in parameters.items():
                placeholder = f"{{{key}}}"
                code = code.replace(placeholder, str(value))
            return code
        except Exception as e:
            logger.error(f"代码生成失败: {str(e)}")
            return template

    def convert_to_python(self, pine_code):
        """转换Pine Script到Python"""
        return {
            "success": False,
            "message": "Pine Script转换功能暂未实现"
        }

def get_current_user():
    """获取当前用户（简化版本）"""
    # 这里应该实现真正的用户认证逻辑
    # 暂时返回一个模拟用户ID
    return {"id": 1, "username": "admin", "role": "admin"}

# ==================== 策略CRUD API ====================

@strategy_bp.route('/strategies', methods=['GET'])
def get_strategies():
    """获取策略列表"""
    try:
        # 获取查询参数
        strategy_type = request.args.get('type')
        category = request.args.get('category')
        status = request.args.get('status')
        is_active = request.args.get('is_active')
        skip = int(request.args.get('skip', 0))
        limit = int(request.args.get('limit', 100))

        # 获取当前用户
        current_user = get_current_user()
        creator_id = current_user["id"] if current_user["role"] != "admin" else None

        # 转换is_active参数
        if is_active is not None:
            is_active = is_active.lower() == 'true'

        # 获取策略列表
        db = get_db()
        strategy_service = StrategyService(db)
        strategies = strategy_service.get_strategies(
            creator_id=creator_id,
            strategy_type=strategy_type,
            category=category,
            status=status,
            is_active=is_active,
            skip=skip,
            limit=limit
        )

        # 转换为响应格式
        strategy_list = []
        for strategy in strategies:
            # 策略已经是字典格式
            if isinstance(strategy, dict):
                strategy_dict = {
                    "id": strategy.get('id'),
                    "name": strategy.get('name'),
                    "type": strategy.get('type'),
                    "category": strategy.get('category'),
                    "description": strategy.get('description'),
                    "code_type": strategy.get('code_type'),
                    "symbol": strategy.get('symbol'),
                    "timeframe": strategy.get('timeframe'),
                    "status": strategy.get('status'),
                    "is_active": strategy.get('is_active'),
                    "created_at": strategy.get('created_at'),
                    "updated_at": strategy.get('updated_at')
                }
            else:
                # 如果是对象格式
                strategy_dict = {
                    "id": strategy.id,
                    "name": strategy.name,
                    "type": strategy.type,
                    "category": strategy.category,
                    "description": strategy.description,
                    "code_type": strategy.code_type,
                    "symbol": strategy.symbol,
                    "timeframe": strategy.timeframe,
                    "status": strategy.status,
                    "is_active": strategy.is_active,
                    "created_at": strategy.created_at.isoformat() if hasattr(strategy.created_at, 'isoformat') else strategy.created_at,
                    "updated_at": strategy.updated_at.isoformat() if hasattr(strategy.updated_at, 'isoformat') else strategy.updated_at
                }
            strategy_list.append(strategy_dict)

        return jsonify({
            "success": True,
            "data": strategy_list,
            "total": len(strategy_list),
            "message": "获取策略列表成功"
        })

    except Exception as e:
        logger.error(f"获取策略列表失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "获取策略列表失败"
        }), 500

@strategy_bp.route('/strategies', methods=['POST'])
def create_strategy():
    """创建新策略"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "error": "请求数据为空",
                "message": "创建策略失败"
            }), 400

        # 获取当前用户
        current_user = get_current_user()

        # 验证必需字段
        required_fields = ['name', 'type', 'category']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    "success": False,
                    "error": f"缺少必需字段: {field}",
                    "message": "创建策略失败"
                }), 400

        # 创建策略
        db = get_db()
        strategy_service = StrategyService(db)

        # 直接使用字典数据
        strategy_data = {
            'name': data['name'],
            'type': data['type'],
            'category': data['category'],
            'description': data.get('description'),
            'code_type': data.get('code_type', 'python'),
            'code_content': data.get('code_content'),
            'parameters': data.get('parameters'),
            'template_id': data.get('template_id'),
            'symbol': data.get('symbol'),
            'timeframe': data.get('timeframe')
        }

        strategy = strategy_service.create_strategy(strategy_data, current_user["id"])

        # 处理返回结果
        if isinstance(strategy, dict):
            return jsonify({
                "success": True,
                "data": {
                    "id": strategy.get('id'),
                    "name": strategy.get('name'),
                    "type": strategy.get('type'),
                    "category": strategy.get('category'),
                    "status": strategy.get('status')
                },
                "message": "策略创建成功"
            }), 201
        else:
            return jsonify({
                "success": True,
                "data": {
                    "id": strategy.id,
                    "name": strategy.name,
                    "type": strategy.type,
                    "category": strategy.category,
                    "status": strategy.status
                },
                "message": "策略创建成功"
            }), 201

    except ValueError as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "创建策略失败"
        }), 400
    except Exception as e:
        logger.error(f"创建策略失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "创建策略失败"
        }), 500

@strategy_bp.route('/strategies/<int:strategy_id>', methods=['GET'])
def get_strategy(strategy_id):
    """获取策略详情"""
    try:
        current_user = get_current_user()
        creator_id = current_user["id"] if current_user["role"] != "admin" else None

        db = get_db()
        strategy_service = StrategyService(db)
        strategy = strategy_service.get_strategy(strategy_id, creator_id)

        if not strategy:
            return jsonify({
                "success": False,
                "error": "策略不存在",
                "message": "获取策略详情失败"
            }), 404

        # 处理策略数据
        if isinstance(strategy, dict):
            strategy_dict = {
                "id": strategy.get('id'),
                "name": strategy.get('name'),
                "type": strategy.get('type'),
                "category": strategy.get('category'),
                "description": strategy.get('description'),
                "code_type": strategy.get('code_type'),
                "code_content": strategy.get('code_content'),
                "parameters": strategy.get('parameters'),
                "template_id": strategy.get('template_id'),
                "symbol": strategy.get('symbol'),
                "timeframe": strategy.get('timeframe'),
                "file_path": strategy.get('file_path'),
                "status": strategy.get('status'),
                "is_active": strategy.get('is_active'),
                "created_by": strategy.get('creator_id'),
                "created_at": strategy.get('created_at'),
                "updated_at": strategy.get('updated_at')
            }
        else:
            strategy_dict = {
                "id": strategy.id,
                "name": strategy.name,
                "type": strategy.type,
                "category": strategy.category,
                "description": strategy.description,
                "code_type": strategy.code_type,
                "code_content": strategy.code_content,
                "parameters": strategy.parameters,
                "template_id": strategy.template_id,
                "symbol": strategy.symbol,
                "timeframe": strategy.timeframe,
                "file_path": strategy.file_path,
                "status": strategy.status,
                "is_active": strategy.is_active,
                "created_by": strategy.creator_id,
                "created_at": strategy.created_at.isoformat() if hasattr(strategy.created_at, 'isoformat') else strategy.created_at,
                "updated_at": strategy.updated_at.isoformat() if hasattr(strategy.updated_at, 'isoformat') else strategy.updated_at
            }

        return jsonify({
            "success": True,
            "data": strategy_dict,
            "message": "获取策略详情成功"
        })

    except Exception as e:
        logger.error(f"获取策略详情失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "获取策略详情失败"
        }), 500

@strategy_bp.route('/strategies/<int:strategy_id>', methods=['PUT'])
def update_strategy(strategy_id):
    """更新策略"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "error": "请求数据为空",
                "message": "更新策略失败"
            }), 400

        current_user = get_current_user()

        db = get_db()
        strategy_service = StrategyService(db)

        # 构建更新数据
        strategy_data = StrategyUpdate(**data)

        strategy = strategy_service.update_strategy(strategy_id, strategy_data, current_user["id"])

        if not strategy:
            return jsonify({
                "success": False,
                "error": "策略不存在",
                "message": "更新策略失败"
            }), 404

        return jsonify({
            "success": True,
            "data": {
                "id": strategy.id,
                "name": strategy.name,
                "status": strategy.status,
                "updated_at": strategy.updated_at.isoformat()
            },
            "message": "策略更新成功"
        })

    except ValueError as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "更新策略失败"
        }), 400
    except Exception as e:
        logger.error(f"更新策略失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "更新策略失败"
        }), 500

@strategy_bp.route('/strategies/<int:strategy_id>', methods=['DELETE'])
def delete_strategy(strategy_id):
    """删除策略"""
    try:
        current_user = get_current_user()

        db = get_db()
        strategy_service = StrategyService(db)

        success = strategy_service.delete_strategy(strategy_id, current_user["id"])

        if not success:
            return jsonify({
                "success": False,
                "error": "策略不存在",
                "message": "删除策略失败"
            }), 404

        return jsonify({
            "success": True,
            "message": "策略删除成功"
        })

    except Exception as e:
        logger.error(f"删除策略失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "删除策略失败"
        }), 500

# ==================== 策略模板API ====================

@strategy_bp.route('/strategy-templates', methods=['GET'])
def get_strategy_templates():
    """获取策略模板列表"""
    try:
        strategy_type = request.args.get('type')
        category = request.args.get('category')
        is_builtin = request.args.get('is_builtin')
        skip = int(request.args.get('skip', 0))
        limit = int(request.args.get('limit', 100))

        if is_builtin is not None:
            is_builtin = is_builtin.lower() == 'true'

        db = get_db()
        template_service = TemplateService(db)
        templates = template_service.get_templates(
            strategy_type=strategy_type,
            category=category,
            is_builtin=is_builtin,
            skip=skip,
            limit=limit
        )

        template_list = []
        for template in templates:
            # 模板已经是字典格式
            if isinstance(template, dict):
                template_dict = {
                    "id": template.get('id'),
                    "name": template.get('name'),
                    "type": template.get('type'),
                    "category": template.get('category'),
                    "description": template.get('description'),
                    "parameter_schema": template.get('parameter_schema'),
                    "default_parameters": template.get('default_parameters'),
                    "is_builtin": template.get('is_builtin'),
                    "created_at": template.get('created_at')
                }
            else:
                # 如果是对象格式
                template_dict = {
                    "id": template.id,
                    "name": template.name,
                    "type": template.type,
                    "category": template.category,
                    "description": template.description,
                    "parameter_schema": template.parameter_schema,
                    "default_parameters": template.default_parameters,
                    "is_builtin": template.is_builtin,
                    "created_at": template.created_at.isoformat() if hasattr(template.created_at, 'isoformat') else template.created_at
                }
            template_list.append(template_dict)

        return jsonify({
            "success": True,
            "data": template_list,
            "total": len(template_list),
            "message": "获取模板列表成功"
        })

    except Exception as e:
        logger.error(f"获取模板列表失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "获取模板列表失败"
        }), 500

@strategy_bp.route('/strategy-templates/<int:template_id>/generate', methods=['POST'])
def generate_strategy_from_template(template_id):
    """从模板生成策略代码"""
    try:
        data = request.get_json()
        if not data or 'parameters' not in data:
            return jsonify({
                "success": False,
                "error": "缺少参数数据",
                "message": "代码生成失败"
            }), 400

        db = get_db()
        strategy_service = StrategyService(db)

        result = strategy_service.generate_code_from_template(
            template_id, data['parameters']
        )

        # 处理返回结果
        if isinstance(result, dict):
            return jsonify(result)
        else:
            return jsonify({
                "success": result.success,
                "code": result.code,
                "message": result.message,
                "errors": result.errors
            })

    except Exception as e:
        logger.error(f"代码生成失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "代码生成失败"
        }), 500

# ==================== 代码验证和转换API ====================

@strategy_bp.route('/strategies/validate-code', methods=['POST'])
def validate_strategy_code():
    """验证策略代码"""
    try:
        data = request.get_json()
        if not data or 'code' not in data:
            return jsonify({
                "success": False,
                "error": "缺少代码数据",
                "message": "代码验证失败"
            }), 400

        code = data['code']
        code_type = data.get('code_type', 'python')

        # 简单的代码验证
        errors = []
        warnings = []

        if code_type == 'python':
            # Python代码语法检查
            try:
                compile(code, '<string>', 'exec')
            except SyntaxError as e:
                errors.append(f"Python语法错误: {str(e)}")

        # 检查必需的函数
        required_functions = ['initialize', 'handle_bar']
        for func in required_functions:
            if f'def {func}(' not in code:
                warnings.append(f"建议定义函数: {func}")

        is_valid = len(errors) == 0

        return jsonify({
            "success": True,
            "valid": is_valid,
            "errors": errors,
            "warnings": warnings,
            "message": "代码验证完成"
        })

    except Exception as e:
        logger.error(f"代码验证失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "代码验证失败"
        }), 500

@strategy_bp.route('/strategies/convert-pinescript', methods=['POST'])
def convert_pinescript():
    """转换Pine Script代码"""
    try:
        data = request.get_json()
        if not data or 'pine_code' not in data:
            return jsonify({
                "success": False,
                "error": "缺少Pine Script代码",
                "message": "代码转换失败"
            }), 400

        pine_code = data['pine_code']

        # 使用代码生成器进行转换（包含兼容性检查）
        code_generator = CodeGenerator()
        result = code_generator.convert_to_python(pine_code)

        return jsonify(result)

    except Exception as e:
        logger.error(f"Pine Script转换失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "代码转换失败"
        }), 500

@strategy_bp.route('/strategies/check-pinescript-compatibility', methods=['POST'])
def check_pinescript_compatibility():
    """检查Pine Script代码兼容性"""
    try:
        data = request.get_json()
        if not data or 'pine_code' not in data:
            return jsonify({
                "success": False,
                "error": "缺少Pine Script代码",
                "message": "兼容性检查失败"
            }), 400

        pine_code = data['pine_code']

        # 使用代码生成器检查兼容性
        code_generator = CodeGenerator()
        result = code_generator.check_compatibility(pine_code)

        return jsonify({
            "success": True,
            "compatibility_score": result["score"],
            "warnings": result["warnings"],
            "unsupported_features": result["unsupported_features"],
            "recommendations": result["recommendations"],
            "message": "兼容性检查完成"
        })

    except Exception as e:
        logger.error(f"Pine Script兼容性检查失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "兼容性检查失败"
        }), 500

# ==================== 策略参数历史API ====================

@strategy_bp.route('/strategies/<int:strategy_id>/parameter-history', methods=['GET'])
def get_parameter_history(strategy_id):
    """获取策略参数变更历史"""
    try:
        db = next(get_db())
        strategy_service = StrategyService(db)
        history = strategy_service.get_parameter_history(strategy_id)

        history_list = []
        for record in history:
            history_dict = {
                "id": record.id,
                "parameters": record.parameters,
                "changed_by": record.changed_by,
                "change_reason": record.change_reason,
                "created_at": record.created_at.isoformat()
            }
            history_list.append(history_dict)

        return jsonify({
            "success": True,
            "data": history_list,
            "message": "获取参数历史成功"
        })

    except Exception as e:
        logger.error(f"获取参数历史失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "获取参数历史失败"
        }), 500

# ==================== 策略统计API ====================

@strategy_bp.route('/strategies/stats', methods=['GET'])
def get_strategy_stats():
    """获取策略统计信息"""
    try:
        current_user = get_current_user()
        creator_id = current_user["id"] if current_user["role"] != "admin" else None

        db = get_db()
        strategy_service = StrategyService(db)

        # 获取所有策略
        all_strategies = strategy_service.get_strategies(creator_id=creator_id, limit=1000)

        # 计算统计数据
        total = len(all_strategies)
        active = len([s for s in all_strategies if getattr(s, 'status', None) == 'active'])
        inactive = len([s for s in all_strategies if getattr(s, 'status', None) in ['inactive', 'stopped']])

        stats = {
            "total": total,
            "active": active,
            "inactive": inactive,
            "created": len([s for s in all_strategies if getattr(s, 'status', None) == 'created']),
            "validated": len([s for s in all_strategies if getattr(s, 'status', None) == 'validated']),
            "error": len([s for s in all_strategies if getattr(s, 'status', None) == 'error'])
        }

        return jsonify({
            "success": True,
            "data": stats,
            "message": "获取策略统计成功"
        })

    except Exception as e:
        logger.error(f"获取策略统计失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "获取策略统计失败"
        }), 500

# ==================== 策略类型和分类API ====================

@strategy_bp.route('/strategy-types', methods=['GET'])
def get_strategy_types():
    """获取策略类型列表"""
    try:
        strategy_types = [
            {
                "value": "trend_following",
                "label": "趋势跟踪",
                "description": "追踪市场趋势的策略",
                "categories": [
                    {"value": "dual_ma_cross", "label": "双均线交叉"},
                    {"value": "triple_ma", "label": "三均线"},
                    {"value": "macd_trend", "label": "MACD趋势"},
                    {"value": "bollinger_breakout", "label": "布林带突破"}
                ]
            },
            {
                "value": "mean_reversion",
                "label": "均值回归",
                "description": "基于价格回归均值的策略",
                "categories": [
                    {"value": "rsi_oversold", "label": "RSI超买超卖"},
                    {"value": "bollinger_reversion", "label": "布林带回归"},
                    {"value": "support_resistance", "label": "支撑阻力"}
                ]
            },
            {
                "value": "momentum",
                "label": "动量策略",
                "description": "基于价格动量的策略",
                "categories": [
                    {"value": "price_momentum", "label": "价格动量"},
                    {"value": "volume_momentum", "label": "成交量动量"},
                    {"value": "relative_strength", "label": "相对强弱"}
                ]
            },
            {
                "value": "arbitrage",
                "label": "套利策略",
                "description": "利用价格差异的策略",
                "categories": [
                    {"value": "statistical_arbitrage", "label": "统计套利"},
                    {"value": "triangular_arbitrage", "label": "三角套利"}
                ]
            },
            {
                "value": "grid",
                "label": "网格策略",
                "description": "基于网格交易的策略",
                "categories": [
                    {"value": "fixed_grid", "label": "固定网格"},
                    {"value": "dynamic_grid", "label": "动态网格"},
                    {"value": "martingale", "label": "马丁格尔"}
                ]
            },
            {
                "value": "custom",
                "label": "自定义策略",
                "description": "用户自定义的策略",
                "categories": [
                    {"value": "python_custom", "label": "Python自定义"},
                    {"value": "pinescript_custom", "label": "Pine Script自定义"},
                    {"value": "ml_strategy", "label": "机器学习策略"}
                ]
            }
        ]

        return jsonify({
            "success": True,
            "data": strategy_types,
            "message": "获取策略类型成功"
        })

    except Exception as e:
        logger.error(f"获取策略类型失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "获取策略类型失败"
        }), 500

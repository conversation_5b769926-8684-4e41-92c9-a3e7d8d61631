[HMR] Waiting for update signal from WDS...
websocket.js:48 [WebSocket] 初始化客户端
market.js:6 使用市场数据服务URL: http://localhost:8005
vue.runtime.esm.js:8809 Download the Vue Devtools extension for a better development experience:
https://github.com/vuejs/vue-devtools
index.js:442 [Vue warn]: Error in setup: "TypeError: Object(...) is not a function"

found in

---> <StrategyManagement> at src/views/strategy/StrategyManagement.vue
       <Layout> at src/views/Layout.vue
         <App> at src/App.vue
           <Root>
warn @ vue.runtime.esm.js:4625
logError @ vue.runtime.esm.js:3061
globalHandleError @ vue.runtime.esm.js:3057
handleError @ vue.runtime.esm.js:3024
invokeWithErrorHandling @ vue.runtime.esm.js:3040
initSetup @ vue.runtime.esm.js:2457
initState @ vue.runtime.esm.js:5385
(anonymous) @ vue.runtime.esm.js:5714
VueComponent @ vue.runtime.esm.js:5849
createComponentInstanceForVnode @ vue.runtime.esm.js:4564
init @ vue.runtime.esm.js:4426
merged @ vue.runtime.esm.js:4581
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
patch @ vue.runtime.esm.js:7095
(anonymous) @ vue.runtime.esm.js:3781
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
patch @ vue.runtime.esm.js:7095
(anonymous) @ vue.runtime.esm.js:3781
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
merged @ vue.runtime.esm.js:4581
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
updateChildren @ vue.runtime.esm.js:6840
patchVnode @ vue.runtime.esm.js:6933
updateChildren @ vue.runtime.esm.js:6807
patchVnode @ vue.runtime.esm.js:6933
patch @ vue.runtime.esm.js:7101
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
index.js:442 TypeError: Object(...) is not a function
    at setup (StrategyManagement.vue:202:1)
    at invokeWithErrorHandling (vue.runtime.esm.js:3033:1)
    at initSetup (vue.runtime.esm.js:2457:1)
    at initState (vue.runtime.esm.js:5385:1)
    at VueComponent._init (vue.runtime.esm.js:5714:1)
    at new VueComponent (vue.runtime.esm.js:5849:1)
    at createComponentInstanceForVnode (vue.runtime.esm.js:4564:1)
    at init (vue.runtime.esm.js:4426:1)
    at merged (vue.runtime.esm.js:4581:1)
    at createComponent (vue.runtime.esm.js:6591:1)
logError @ vue.runtime.esm.js:3065
globalHandleError @ vue.runtime.esm.js:3057
handleError @ vue.runtime.esm.js:3024
invokeWithErrorHandling @ vue.runtime.esm.js:3040
initSetup @ vue.runtime.esm.js:2457
initState @ vue.runtime.esm.js:5385
(anonymous) @ vue.runtime.esm.js:5714
VueComponent @ vue.runtime.esm.js:5849
createComponentInstanceForVnode @ vue.runtime.esm.js:4564
init @ vue.runtime.esm.js:4426
merged @ vue.runtime.esm.js:4581
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
patch @ vue.runtime.esm.js:7095
(anonymous) @ vue.runtime.esm.js:3781
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
patch @ vue.runtime.esm.js:7095
(anonymous) @ vue.runtime.esm.js:3781
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
merged @ vue.runtime.esm.js:4581
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
updateChildren @ vue.runtime.esm.js:6840
patchVnode @ vue.runtime.esm.js:6933
updateChildren @ vue.runtime.esm.js:6807
patchVnode @ vue.runtime.esm.js:6933
patch @ vue.runtime.esm.js:7101
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
StrategyManagement.vue:18 [Vue warn]: Property or method "createStrategy" is not defined on the instance but referenced during render. Make sure that this property is reactive, either in the data option, or for class-based components, by initializing the property. See: https://v2.vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.

found in

---> <StrategyManagement> at src/views/strategy/StrategyManagement.vue
       <Layout> at src/views/Layout.vue
         <App> at src/App.vue
           <Root>
warn @ vue.runtime.esm.js:4625
warnNonPresent_1 @ vue.runtime.esm.js:5297
get @ vue.runtime.esm.js:5347
render @ StrategyManagement.vue:18
(anonymous) @ vue.runtime.esm.js:2700
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
merged @ vue.runtime.esm.js:4581
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
patch @ vue.runtime.esm.js:7095
(anonymous) @ vue.runtime.esm.js:3781
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
patch @ vue.runtime.esm.js:7095
(anonymous) @ vue.runtime.esm.js:3781
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
merged @ vue.runtime.esm.js:4581
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
updateChildren @ vue.runtime.esm.js:6840
patchVnode @ vue.runtime.esm.js:6933
updateChildren @ vue.runtime.esm.js:6807
patchVnode @ vue.runtime.esm.js:6933
patch @ vue.runtime.esm.js:7101
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
StrategyManagement.vue:40 [Vue warn]: Property or method "stats" is not defined on the instance but referenced during render. Make sure that this property is reactive, either in the data option, or for class-based components, by initializing the property. See: https://v2.vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.

found in

---> <StrategyManagement> at src/views/strategy/StrategyManagement.vue
       <Layout> at src/views/Layout.vue
         <App> at src/App.vue
           <Root>
warn @ vue.runtime.esm.js:4625
warnNonPresent_1 @ vue.runtime.esm.js:5297
get @ vue.runtime.esm.js:5347
render @ StrategyManagement.vue:40
(anonymous) @ vue.runtime.esm.js:2700
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
merged @ vue.runtime.esm.js:4581
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
patch @ vue.runtime.esm.js:7095
(anonymous) @ vue.runtime.esm.js:3781
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
patch @ vue.runtime.esm.js:7095
(anonymous) @ vue.runtime.esm.js:3781
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
merged @ vue.runtime.esm.js:4581
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
updateChildren @ vue.runtime.esm.js:6840
patchVnode @ vue.runtime.esm.js:6933
updateChildren @ vue.runtime.esm.js:6807
patchVnode @ vue.runtime.esm.js:6933
patch @ vue.runtime.esm.js:7101
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
index.js:442 [Vue warn]: Error in render: "TypeError: Cannot read properties of undefined (reading 'total')"

found in

---> <StrategyManagement> at src/views/strategy/StrategyManagement.vue
       <Layout> at src/views/Layout.vue
         <App> at src/App.vue
           <Root>
warn @ vue.runtime.esm.js:4625
logError @ vue.runtime.esm.js:3061
globalHandleError @ vue.runtime.esm.js:3057
handleError @ vue.runtime.esm.js:3024
(anonymous) @ vue.runtime.esm.js:2703
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
merged @ vue.runtime.esm.js:4581
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
patch @ vue.runtime.esm.js:7095
(anonymous) @ vue.runtime.esm.js:3781
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
patch @ vue.runtime.esm.js:7095
(anonymous) @ vue.runtime.esm.js:3781
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
merged @ vue.runtime.esm.js:4581
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
updateChildren @ vue.runtime.esm.js:6840
patchVnode @ vue.runtime.esm.js:6933
updateChildren @ vue.runtime.esm.js:6807
patchVnode @ vue.runtime.esm.js:6933
patch @ vue.runtime.esm.js:7101
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
index.js:442 TypeError: Cannot read properties of undefined (reading 'total')
    at Proxy.render (StrategyManagement.vue:40:47)
    at VueComponent._render (vue.runtime.esm.js:2700:1)
    at VueComponent.updateComponent (vue.runtime.esm.js:3891:1)
    at Watcher.get (vue.runtime.esm.js:3462:1)
    at new Watcher (vue.runtime.esm.js:3452:1)
    at mountComponent (vue.runtime.esm.js:3908:1)
    at VueComponent.$mount (vue.runtime.esm.js:8797:1)
    at init (vue.runtime.esm.js:4427:1)
    at merged (vue.runtime.esm.js:4581:1)
    at createComponent (vue.runtime.esm.js:6591:1)
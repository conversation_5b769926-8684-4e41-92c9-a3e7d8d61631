[HMR] Waiting for update signal from WDS...
websocket.js:48 [WebSocket] 初始化客户端
market.js:6 使用市场数据服务URL: http://localhost:8005
vue.runtime.esm.js:8809 Download the Vue Devtools extension for a better development experience:
https://github.com/vuejs/vue-devtools
vue.runtime.esm.js:4625 [Vue warn]: Error in setup: "TypeError: Object(...) is not a function"

found in

---> <StrategyManagement> at src/views/strategy/StrategyManagement.vue
       <Layout> at src/views/Layout.vue
         <App> at src/App.vue
           <Root>
warn @ vue.runtime.esm.js:4625Understand this error
vue.runtime.esm.js:3065 TypeError: Object(...) is not a function
    at setup (StrategyManagement.vue:202:1)
    at invokeWithErrorHandling (vue.runtime.esm.js:3033:1)
    at initSetup (vue.runtime.esm.js:2457:1)
    at initState (vue.runtime.esm.js:5385:1)
    at Vue._init (vue.runtime.esm.js:5714:1)
    at new VueComponent (vue.runtime.esm.js:5849:1)
    at createComponentInstanceForVnode (vue.runtime.esm.js:4564:1)
    at init (vue.runtime.esm.js:4426:1)
    at merged (vue.runtime.esm.js:4581:1)
    at createComponent (vue.runtime.esm.js:6591:1)
logError @ vue.runtime.esm.js:3065Understand this error
vue.runtime.esm.js:4625 [Vue warn]: Property or method "createStrategy" is not defined on the instance but referenced during render. Make sure that this property is reactive, either in the data option, or for class-based components, by initializing the property. See: https://v2.vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.

found in

---> <StrategyManagement> at src/views/strategy/StrategyManagement.vue
       <Layout> at src/views/Layout.vue
         <App> at src/App.vue
           <Root>
warn @ vue.runtime.esm.js:4625Understand this error
3vue.runtime.esm.js:4625 [Vue warn]: Property or method "stats" is not defined on the instance but referenced during render. Make sure that this property is reactive, either in the data option, or for class-based components, by initializing the property. See: https://v2.vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.

found in

---> <StrategyManagement> at src/views/strategy/StrategyManagement.vue
       <Layout> at src/views/Layout.vue
         <App> at src/App.vue
           <Root>
warn @ vue.runtime.esm.js:4625Understand this error
vue.runtime.esm.js:4625 [Vue warn]: Property or method "todayCreated" is not defined on the instance but referenced during render. Make sure that this property is reactive, either in the data option, or for class-based components, by initializing the property. See: https://v2.vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.

found in

---> <StrategyManagement> at src/views/strategy/StrategyManagement.vue
       <Layout> at src/views/Layout.vue
         <App> at src/App.vue
           <Root>
warn @ vue.runtime.esm.js:4625Understand this error
vue.runtime.esm.js:4625 [Vue warn]: Property or method "filterForm" is not defined on the instance but referenced during render. Make sure that this property is reactive, either in the data option, or for class-based components, by initializing the property. See: https://v2.vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.

found in

---> <StrategyManagement> at src/views/strategy/StrategyManagement.vue
       <Layout> at src/views/Layout.vue
         <App> at src/App.vue
           <Root>
warn @ vue.runtime.esm.js:4625Understand this error
vue.runtime.esm.js:4625 [Vue warn]: Property or method "loadStrategies" is not defined on the instance but referenced during render. Make sure that this property is reactive, either in the data option, or for class-based components, by initializing the property. See: https://v2.vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.

found in

---> <StrategyManagement> at src/views/strategy/StrategyManagement.vue
       <Layout> at src/views/Layout.vue
         <App> at src/App.vue
           <Root>
warn @ vue.runtime.esm.js:4625Understand this error
vue.runtime.esm.js:4625 [Vue warn]: Property or method "filterForm" is not defined on the instance but referenced during render. Make sure that this property is reactive, either in the data option, or for class-based components, by initializing the property. See: https://v2.vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.

found in

---> <StrategyManagement> at src/views/strategy/StrategyManagement.vue
       <Layout> at src/views/Layout.vue
         <App> at src/App.vue
           <Root>
warn @ vue.runtime.esm.js:4625Understand this error
vue.runtime.esm.js:4625 [Vue warn]: Error in render: "TypeError: Cannot read properties of undefined (reading 'type')"

found in

---> <StrategyManagement> at src/views/strategy/StrategyManagement.vue
       <Layout> at src/views/Layout.vue
         <App> at src/App.vue
           <Root>
warn @ vue.runtime.esm.js:4625Understand this error
vue.runtime.esm.js:3065 TypeError: Cannot read properties of undefined (reading 'type')
    at Proxy.render (StrategyManagement.vue:153:47)
    at Vue._render (vue.runtime.esm.js:2700:1)
    at VueComponent.updateComponent (vue.runtime.esm.js:3891:1)
    at push../node_modules/vue/dist/vue.runtime.esm.js.Watcher.get (vue.runtime.esm.js:3462:1)
    at new Watcher (vue.runtime.esm.js:3452:1)
    at mountComponent (vue.runtime.esm.js:3908:1)
    at push../node_modules/vue/dist/vue.runtime.esm.js.Vue.$mount (vue.runtime.esm.js:8797:1)
    at init (vue.runtime.esm.js:4427:1)
    at merged (vue.runtime.esm.js:4581:1)
    at createComponent (vue.runtime.esm.js:6591:1)
[HMR] Waiting for update signal from WDS...
websocket.js:48 [WebSocket] 初始化客户端
market.js:6 使用市场数据服务URL: http://localhost:8005
vue.runtime.esm.js:8809 Download the Vue Devtools extension for a better development experience:
https://github.com/vuejs/vue-devtools
StrategyManagement.vue:217 [Vue warn]: Property or method "debounceSearch" is not defined on the instance but referenced during render. Make sure that this property is reactive, either in the data option, or for class-based components, by initializing the property. See: https://v2.vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.

found in

---> <StrategyManagement> at src/views/strategy/StrategyManagement.vue
       <Layout> at src/views/Layout.vue
         <App> at src/App.vue
           <Root>
warn @ vue.runtime.esm.js:4625
warnNonPresent_1 @ vue.runtime.esm.js:5297
get @ vue.runtime.esm.js:5347
render @ StrategyManagement.vue:217
(anonymous) @ vue.runtime.esm.js:2700
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
merged @ vue.runtime.esm.js:4581
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
patch @ vue.runtime.esm.js:7095
(anonymous) @ vue.runtime.esm.js:3781
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
createChildren @ vue.runtime.esm.js:6664
createElm @ vue.runtime.esm.js:6568
patch @ vue.runtime.esm.js:7095
(anonymous) @ vue.runtime.esm.js:3781
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
Watcher @ vue.runtime.esm.js:3452
mountComponent @ vue.runtime.esm.js:3908
(anonymous) @ vue.runtime.esm.js:8797
init @ vue.runtime.esm.js:4427
merged @ vue.runtime.esm.js:4581
createComponent @ vue.runtime.esm.js:6591
createElm @ vue.runtime.esm.js:6545
updateChildren @ vue.runtime.esm.js:6840
patchVnode @ vue.runtime.esm.js:6933
updateChildren @ vue.runtime.esm.js:6807
patchVnode @ vue.runtime.esm.js:6933
patch @ vue.runtime.esm.js:7101
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
StrategyManagement.vue:217 [Vue warn]: Property or method "debounceSearch" is not defined on the instance but referenced during render. Make sure that this property is reactive, either in the data option, or for class-based components, by initializing the property. See: https://v2.vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.

found in

---> <StrategyManagement> at src/views/strategy/StrategyManagement.vue
       <Layout> at src/views/Layout.vue
         <App> at src/App.vue
           <Root>
warn @ vue.runtime.esm.js:4625
warnNonPresent_1 @ vue.runtime.esm.js:5297
get @ vue.runtime.esm.js:5347
render @ StrategyManagement.vue:217
(anonymous) @ vue.runtime.esm.js:2700
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
request.js:31 请求: GET /api/v1/strategies {headers: {…}, params: {…}, data: undefined}
request.js:31 请求: GET /api/v1/strategies/stats {headers: {…}, params: undefined, data: undefined}
request.js:31 请求: GET /api/v1/strategy-types {headers: {…}, params: undefined, data: undefined}
:8081/#/strategy/management:1 Access to XMLHttpRequest at 'http://localhost:8000/api/v1/strategies?page=1&per_page=10&type=&status=&keyword=' from origin 'http://localhost:8081' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: The 'Access-Control-Allow-Origin' header has a value 'http://localhost:8080' that is not equal to the supplied origin.Understand this error
request.js:65 响应错误: {config: {…}, response: 'No response', message: 'Network Error'}
(anonymous) @ request.js:65
Promise.then
request @ Axios.js:88
wrap @ bind.js:9
getStrategies @ strategy.js:12
loadStrategies @ StrategyManagement.vue:288
mounted @ StrategyManagement.vue:273
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
insert @ vue.runtime.esm.js:4443
invokeInsertHook @ vue.runtime.esm.js:6966
patch @ vue.runtime.esm.js:7180
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
request.js:99 网络错误: XMLHttpRequest {onreadystatechange: null, readyState: 4, timeout: 30000, withCredentials: false, upload: XMLHttpRequestUpload, …}
(anonymous) @ request.js:99
Promise.then
request @ Axios.js:88
wrap @ bind.js:9
getStrategies @ strategy.js:12
loadStrategies @ StrategyManagement.vue:288
mounted @ StrategyManagement.vue:273
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
insert @ vue.runtime.esm.js:4443
invokeInsertHook @ vue.runtime.esm.js:6966
patch @ vue.runtime.esm.js:7180
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
StrategyManagement.vue:217 [Vue warn]: Property or method "debounceSearch" is not defined on the instance but referenced during render. Make sure that this property is reactive, either in the data option, or for class-based components, by initializing the property. See: https://v2.vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.

found in

---> <StrategyManagement> at src/views/strategy/StrategyManagement.vue
       <Layout> at src/views/Layout.vue
         <App> at src/App.vue
           <Root>
warn @ vue.runtime.esm.js:4625
warnNonPresent_1 @ vue.runtime.esm.js:5297
get @ vue.runtime.esm.js:5347
render @ StrategyManagement.vue:217
(anonymous) @ vue.runtime.esm.js:2700
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
proxySetter @ vue.runtime.esm.js:5376
Message @ element-ui.common.js:30827
(anonymous) @ element-ui.common.js:30841
loadStrategies @ StrategyManagement.vue:294
await in loadStrategies
mounted @ StrategyManagement.vue:273
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
insert @ vue.runtime.esm.js:4443
invokeInsertHook @ vue.runtime.esm.js:6966
patch @ vue.runtime.esm.js:7180
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
strategy.js:12 
            
            
           GET http://localhost:8000/api/v1/strategies?page=1&per_page=10&type=&status=&keyword= net::ERR_FAILED
dispatchXhrRequest @ xhr.js:187
xhrAdapter @ xhr.js:13
dispatchRequest @ dispatchRequest.js:53
Promise.then
request @ Axios.js:88
wrap @ bind.js:9
getStrategies @ strategy.js:12
loadStrategies @ StrategyManagement.vue:288
mounted @ StrategyManagement.vue:273
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
insert @ vue.runtime.esm.js:4443
invokeInsertHook @ vue.runtime.esm.js:6966
patch @ vue.runtime.esm.js:7180
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
:8081/#/strategy/management:1 Access to XMLHttpRequest at 'http://localhost:8000/api/v1/strategies/stats' from origin 'http://localhost:8081' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: The 'Access-Control-Allow-Origin' header has a value 'http://localhost:8080' that is not equal to the supplied origin.Understand this error
request.js:65 响应错误: {config: {…}, response: 'No response', message: 'Network Error'}
(anonymous) @ request.js:65
Promise.then
request @ Axios.js:88
wrap @ bind.js:9
getStrategyStats @ strategy.js:55
loadStats @ StrategyManagement.vue:303
mounted @ StrategyManagement.vue:274
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
insert @ vue.runtime.esm.js:4443
invokeInsertHook @ vue.runtime.esm.js:6966
patch @ vue.runtime.esm.js:7180
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
request.js:99 网络错误: XMLHttpRequest {onreadystatechange: null, readyState: 4, timeout: 30000, withCredentials: false, upload: XMLHttpRequestUpload, …}
(anonymous) @ request.js:99
Promise.then
request @ Axios.js:88
wrap @ bind.js:9
getStrategyStats @ strategy.js:55
loadStats @ StrategyManagement.vue:303
mounted @ StrategyManagement.vue:274
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
insert @ vue.runtime.esm.js:4443
invokeInsertHook @ vue.runtime.esm.js:6966
patch @ vue.runtime.esm.js:7180
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
StrategyManagement.vue:308 加载统计数据失败: Error: Network Error
    at createError (createError.js:16:1)
    at XMLHttpRequest.handleError (xhr.js:99:1)
loadStats @ StrategyManagement.vue:308
await in loadStats
mounted @ StrategyManagement.vue:274
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
insert @ vue.runtime.esm.js:4443
invokeInsertHook @ vue.runtime.esm.js:6966
patch @ vue.runtime.esm.js:7180
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
strategy.js:55 
            
            
           GET http://localhost:8000/api/v1/strategies/stats net::ERR_FAILED
dispatchXhrRequest @ xhr.js:187
xhrAdapter @ xhr.js:13
dispatchRequest @ dispatchRequest.js:53
Promise.then
request @ Axios.js:88
wrap @ bind.js:9
getStrategyStats @ strategy.js:55
loadStats @ StrategyManagement.vue:303
mounted @ StrategyManagement.vue:274
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
insert @ vue.runtime.esm.js:4443
invokeInsertHook @ vue.runtime.esm.js:6966
patch @ vue.runtime.esm.js:7180
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
:8081/#/strategy/management:1 Access to XMLHttpRequest at 'http://localhost:8000/api/v1/strategy-types' from origin 'http://localhost:8081' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: The 'Access-Control-Allow-Origin' header has a value 'http://localhost:8080' that is not equal to the supplied origin.Understand this error
request.js:65 响应错误: {config: {…}, response: 'No response', message: 'Network Error'}
(anonymous) @ request.js:65
Promise.then
request @ Axios.js:88
wrap @ bind.js:9
getStrategyTypes @ strategy.js:116
loadStrategyTypes @ StrategyManagement.vue:315
mounted @ StrategyManagement.vue:275
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
insert @ vue.runtime.esm.js:4443
invokeInsertHook @ vue.runtime.esm.js:6966
patch @ vue.runtime.esm.js:7180
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
request.js:99 网络错误: XMLHttpRequest {onreadystatechange: null, readyState: 4, timeout: 30000, withCredentials: false, upload: XMLHttpRequestUpload, …}
(anonymous) @ request.js:99
Promise.then
request @ Axios.js:88
wrap @ bind.js:9
getStrategyTypes @ strategy.js:116
loadStrategyTypes @ StrategyManagement.vue:315
mounted @ StrategyManagement.vue:275
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
insert @ vue.runtime.esm.js:4443
invokeInsertHook @ vue.runtime.esm.js:6966
patch @ vue.runtime.esm.js:7180
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
StrategyManagement.vue:320 加载策略类型失败: Error: Network Error
    at createError (createError.js:16:1)
    at XMLHttpRequest.handleError (xhr.js:99:1)
loadStrategyTypes @ StrategyManagement.vue:320
await in loadStrategyTypes
mounted @ StrategyManagement.vue:275
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
insert @ vue.runtime.esm.js:4443
invokeInsertHook @ vue.runtime.esm.js:6966
patch @ vue.runtime.esm.js:7180
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
strategy.js:116 
            
            
           GET http://localhost:8000/api/v1/strategy-types net::ERR_FAILED
dispatchXhrRequest @ xhr.js:187
xhrAdapter @ xhr.js:13
dispatchRequest @ dispatchRequest.js:53
Promise.then
request @ Axios.js:88
wrap @ bind.js:9
getStrategyTypes @ strategy.js:116
loadStrategyTypes @ StrategyManagement.vue:315
mounted @ StrategyManagement.vue:275
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
insert @ vue.runtime.esm.js:4443
invokeInsertHook @ vue.runtime.esm.js:6966
patch @ vue.runtime.esm.js:7180
(anonymous) @ vue.runtime.esm.js:3785
updateComponent @ vue.runtime.esm.js:3891
(anonymous) @ vue.runtime.esm.js:3462
(anonymous) @ vue.runtime.esm.js:3538
flushSchedulerQueue @ vue.runtime.esm.js:4141
(anonymous) @ vue.runtime.esm.js:3159
flushCallbacks @ vue.runtime.esm.js:3081
Promise.then
timerFunc @ vue.runtime.esm.js:3106
nextTick @ vue.runtime.esm.js:3171
queueWatcher @ vue.runtime.esm.js:4227
(anonymous) @ vue.runtime.esm.js:3529
(anonymous) @ vue.runtime.esm.js:732
reactiveSetter @ vue.runtime.esm.js:967
(anonymous) @ vue-router.esm.js:3005
(anonymous) @ vue-router.esm.js:3004
updateRoute @ vue-router.esm.js:2414
(anonymous) @ vue-router.esm.js:2263
(anonymous) @ vue-router.esm.js:2402
step @ vue-router.esm.js:2084
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
runQueue @ vue-router.esm.js:2095
(anonymous) @ vue-router.esm.js:2397
step @ vue-router.esm.js:2084
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ vue-router.esm.js:2127
(anonymous) @ vue-router.esm.js:2203
Promise.then
(anonymous) @ vue-router.esm.js:2150
(anonymous) @ vue-router.esm.js:2171
(anonymous) @ vue-router.esm.js:2171
flatMapComponents @ vue-router.esm.js:2170
(anonymous) @ vue-router.esm.js:2106
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
step @ vue-router.esm.js:2091
step @ vue-router.esm.js:2091
(anonymous) @ vue-router.esm.js:2088
(anonymous) @ vue-router.esm.js:2384
(anonymous) @ index.js:442
iterator @ vue-router.esm.js:2362
step @ vue-router.esm.js:2087
runQueue @ vue-router.esm.js:2095
confirmTransition @ vue-router.esm.js:2392
transitionTo @ vue-router.esm.js:2260
init @ vue-router.esm.js:2996
beforeCreate @ vue-router.esm.js:1298
invokeWithErrorHandling @ vue.runtime.esm.js:3033
callHook$1 @ vue.runtime.esm.js:4048
(anonymous) @ vue.runtime.esm.js:5712
Vue @ vue.runtime.esm.js:5785
./src/main.js @ main.js:126
__webpack_require__ @ bootstrap:853
fn @ bootstrap:150
1 @ StrategyForm.vue:1
__webpack_require__ @ bootstrap:853
checkDeferredModules @ bootstrap:45
(anonymous) @ bootstrap:993
(anonymous) @ bootstrap:993Understand this error
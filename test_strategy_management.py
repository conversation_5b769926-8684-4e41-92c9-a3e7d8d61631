#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略管理功能测试脚本
测试策略选择和策略列表的一致性问题
"""

import requests
import json
import sys
import time

def test_strategy_api():
    """测试策略API功能"""
    print("=" * 60)
    print("策略管理功能测试")
    print("=" * 60)

    base_url = "http://localhost:8000"
    frontend_url = "http://localhost:8081"

    # 测试1: 直接访问后端策略API
    print("\n1. 测试后端策略API...")
    try:
        response = requests.get(f"{base_url}/api/v1/strategy")
        if response.status_code == 200:
            strategies = response.json()
            print(f"✓ 后端API返回 {len(strategies)} 个策略")
            for strategy in strategies:
                print(f"  - ID: {strategy['id']}, 名称: {strategy['name']}, 状态: {strategy['status']}")
        else:
            print(f"✗ 后端API调用失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 后端API调用异常: {e}")
        return False

    # 测试2: 通过前端代理访问策略API
    print("\n2. 测试前端代理策略API...")
    try:
        response = requests.get(f"{frontend_url}/api/v1/strategy")
        if response.status_code == 200:
            frontend_strategies = response.json()
            print(f"✓ 前端代理返回 {len(frontend_strategies)} 个策略")

            # 比较前后端数据一致性
            if len(strategies) == len(frontend_strategies):
                print("✓ 前后端策略数量一致")

                # 检查策略ID和名称是否一致
                backend_ids = {s['id']: s['name'] for s in strategies}
                frontend_ids = {s['id']: s['name'] for s in frontend_strategies}

                if backend_ids == frontend_ids:
                    print("✓ 前后端策略ID和名称完全一致")
                else:
                    print("✗ 前后端策略数据不一致")
                    print(f"  后端: {backend_ids}")
                    print(f"  前端: {frontend_ids}")
                    return False
            else:
                print(f"✗ 前后端策略数量不一致: 后端{len(strategies)}, 前端{len(frontend_strategies)}")
                return False
        else:
            print(f"✗ 前端代理调用失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 前端代理调用异常: {e}")
        return False

    # 测试3: 测试策略详情API
    print("\n3. 测试策略详情API...")
    if strategies:
        strategy_id = strategies[0]['id']
        try:
            response = requests.get(f"{base_url}/api/v1/strategy/{strategy_id}")
            if response.status_code == 200:
                strategy_detail = response.json()
                print(f"✓ 策略详情API正常，策略ID: {strategy_detail['id']}")
            else:
                print(f"✗ 策略详情API调用失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 策略详情API调用异常: {e}")
            return False

    # 测试4: 测试策略统计API
    print("\n4. 测试策略统计API...")
    try:
        response = requests.get(f"{base_url}/api/v1/strategy/stats")
        if response.status_code == 200:
            stats_response = response.json()
            print(f"✓ 策略统计API正常")

            # 检查响应格式
            if 'data' in stats_response:
                stats = stats_response['data']
                print(f"  总策略数: {stats['total']}")
                print(f"  活跃策略: {stats['active']}")
                print(f"  非活跃策略: {stats['inactive']}")
            else:
                # 兼容直接返回统计数据的格式
                print(f"  总策略数: {stats_response.get('total', 'N/A')}")
                print(f"  活跃策略: {stats_response.get('active', 'N/A')}")
                print(f"  非活跃策略: {stats_response.get('inactive', 'N/A')}")
        else:
            print(f"✗ 策略统计API调用失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 策略统计API调用异常: {e}")
        return False

    print("\n" + "=" * 60)
    print("✓ 所有策略管理功能测试通过！")
    print("✓ 策略选择和策略列表数据一致性问题已修复")
    print("=" * 60)
    return True

def test_data_consistency():
    """测试数据一致性"""
    print("\n5. 数据一致性验证...")

    # 检查策略ID格式
    response = requests.get("http://localhost:8000/api/v1/strategy")
    strategies = response.json()

    print("策略ID格式检查:")
    for strategy in strategies:
        strategy_id = strategy['id']
        if isinstance(strategy_id, int):
            print(f"  ✓ 策略 '{strategy['name']}' ID格式正确: {strategy_id} (整数)")
        else:
            print(f"  ✗ 策略 '{strategy['name']}' ID格式错误: {strategy_id} (非整数)")
            return False

    print("✓ 所有策略ID格式正确，使用数据库真实数据")
    return True

if __name__ == "__main__":
    print("开始策略管理功能测试...")

    # 等待服务启动
    print("等待服务启动...")
    time.sleep(2)

    success = test_strategy_api()
    if success:
        success = test_data_consistency()

    if success:
        print("\n🎉 策略管理功能修复成功！")
        print("📋 修复内容:")
        print("  1. 策略优化页面现在从API获取真实策略数据")
        print("  2. 移除了硬编码的模拟策略数据")
        print("  3. 统一了策略ID格式（数据库整数ID）")
        print("  4. 确保前后端数据一致性")
        print("  5. 策略选择和策略列表现在完全匹配")
        sys.exit(0)
    else:
        print("\n❌ 策略管理功能测试失败")
        sys.exit(1)

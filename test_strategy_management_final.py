#!/usr/bin/env python3
"""
策略管理功能最终测试脚本
验证策略管理页面的所有功能是否正常工作
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"
FRONTEND_ORIGIN = "http://localhost:8081"

def test_api_with_cors(endpoint, method="GET", data=None):
    """测试API端点并验证CORS"""
    url = f"{BASE_URL}{endpoint}"
    headers = {
        'Origin': FRONTEND_ORIGIN,
        'Content-Type': 'application/json'
    }
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, headers=headers, timeout=10)
        elif method == "PUT":
            response = requests.put(url, json=data, headers=headers, timeout=10)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers, timeout=10)
        
        print(f"✓ {method} {endpoint}")
        print(f"  状态码: {response.status_code}")
        
        # 检查CORS头
        cors_header = response.headers.get('Access-Control-Allow-Origin')
        if cors_header:
            print(f"  CORS: {cors_header}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"  响应: 成功 - {result.get('message', '')}")
                return True, result
            else:
                print(f"  响应: 失败 - {result.get('message', '')}")
                return False, result
        else:
            print(f"  响应: HTTP错误 {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"✗ {method} {endpoint}")
        print(f"  错误: {str(e)}")
        return False, None

def main():
    """主测试函数"""
    print("=" * 60)
    print("策略管理功能最终测试")
    print("=" * 60)
    
    all_tests_passed = True
    
    # 测试1: 策略统计API
    print("\n1. 测试策略统计API")
    success, data = test_api_with_cors("/api/v1/strategies/stats")
    if success:
        stats = data.get('data', {})
        print(f"  统计数据: 总数={stats.get('total', 0)}, 活跃={stats.get('active', 0)}")
    else:
        all_tests_passed = False
    
    # 测试2: 策略列表API
    print("\n2. 测试策略列表API")
    success, data = test_api_with_cors("/api/v1/strategies")
    if success:
        strategies = data.get('data', [])
        print(f"  策略数量: {len(strategies)}")
    else:
        all_tests_passed = False
    
    # 测试3: 策略类型API
    print("\n3. 测试策略类型API")
    success, data = test_api_with_cors("/api/v1/strategy-types")
    if success:
        types = data.get('data', [])
        print(f"  策略类型数量: {len(types)}")
        for strategy_type in types[:3]:  # 显示前3个类型
            name = strategy_type.get('name', strategy_type.get('label', ''))
            print(f"    - {name}")
    else:
        all_tests_passed = False
    
    # 测试4: 前端页面访问测试
    print("\n4. 测试前端页面访问")
    try:
        frontend_response = requests.get(f"{FRONTEND_ORIGIN}/#/strategy/management", timeout=10)
        if frontend_response.status_code == 200:
            print("✓ 前端页面可访问")
            print(f"  状态码: {frontend_response.status_code}")
        else:
            print(f"✗ 前端页面访问失败: {frontend_response.status_code}")
            all_tests_passed = False
    except Exception as e:
        print(f"✗ 前端页面访问错误: {e}")
        all_tests_passed = False
    
    # 测试5: 跨域预检请求测试
    print("\n5. 测试CORS预检请求")
    try:
        options_response = requests.options(
            f"{BASE_URL}/api/v1/strategies/stats",
            headers={
                'Origin': FRONTEND_ORIGIN,
                'Access-Control-Request-Method': 'GET',
                'Access-Control-Request-Headers': 'Content-Type'
            },
            timeout=10
        )
        print(f"✓ OPTIONS预检请求")
        print(f"  状态码: {options_response.status_code}")
        
        # 检查CORS响应头
        cors_headers = {
            'Access-Control-Allow-Origin': options_response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': options_response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': options_response.headers.get('Access-Control-Allow-Headers')
        }
        
        for header, value in cors_headers.items():
            if value:
                print(f"  {header}: {value}")
                
    except Exception as e:
        print(f"✗ CORS预检请求错误: {e}")
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 所有测试通过！策略管理页面功能正常")
        print("✅ 前端可以正常访问后端API")
        print("✅ CORS配置正确")
        print("✅ 所有API端点正常响应")
    else:
        print("❌ 部分测试失败，请检查错误信息")
        sys.exit(1)
    
    print("=" * 60)

if __name__ == "__main__":
    main()

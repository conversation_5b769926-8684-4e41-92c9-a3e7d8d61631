<template>
  <div class="strategy-wizard">
    <el-card class="wizard-card">
      <template #header>
        <div class="card-header">
          <span class="title">策略创建向导</span>
          <el-button type="text" @click="$router.go(-1)">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
        </div>
      </template>

      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" finish-status="success" class="wizard-steps">
        <el-step title="选择策略类型" description="选择策略分类和模板"></el-step>
        <el-step title="配置参数" description="设置策略参数"></el-step>
        <el-step title="编写代码" description="编辑策略代码"></el-step>
        <el-step title="验证测试" description="验证策略代码"></el-step>
        <el-step title="保存部署" description="保存并部署策略"></el-step>
      </el-steps>

      <!-- 步骤内容 -->
      <div class="wizard-content">
        <!-- 步骤1: 策略类型选择 -->
        <div v-if="currentStep === 0" class="step-content">
          <h3>选择策略类型</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form :model="strategyForm" label-width="120px">
                <el-form-item label="策略名称" required>
                  <el-input v-model="strategyForm.name" placeholder="请输入策略名称" />
                </el-form-item>
                <el-form-item label="策略描述">
                  <el-input
                    v-model="strategyForm.description"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入策略描述"
                  />
                </el-form-item>
                <el-form-item label="策略类型" required>
                  <el-select v-model="strategyForm.type" placeholder="请选择策略类型" @change="onTypeChange">
                    <el-option
                      v-for="type in strategyTypes"
                      :key="type.value"
                      :label="type.label"
                      :value="type.value"
                    >
                      <span>{{ type.label }}</span>
                      <span style="color: #8492a6; font-size: 13px; margin-left: 10px;">
                        {{ type.description }}
                      </span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="策略分类" required v-if="strategyForm.type">
                  <el-select v-model="strategyForm.category" placeholder="请选择策略分类" @change="onCategoryChange">
                    <el-option
                      v-for="category in currentCategories"
                      :key="category.value"
                      :label="category.label"
                      :value="category.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="代码类型">
                  <el-radio-group v-model="strategyForm.code_type">
                    <el-radio label="python">Python</el-radio>
                    <el-radio label="pinescript">Pine Script</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-col>
            <el-col :span="12">
              <div class="template-selection">
                <h4>选择策略模板</h4>
                <el-radio-group v-model="selectedTemplate" class="template-list">
                  <el-radio
                    v-for="template in availableTemplates"
                    :key="template.id"
                    :label="template.id"
                    class="template-item"
                  >
                    <div class="template-info">
                      <div class="template-name">{{ template.name }}</div>
                      <div class="template-desc">{{ template.description }}</div>
                    </div>
                  </el-radio>
                  <el-radio :label="null" class="template-item">
                    <div class="template-info">
                      <div class="template-name">自定义策略</div>
                      <div class="template-desc">从空白模板开始创建</div>
                    </div>
                  </el-radio>
                </el-radio-group>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 步骤2: 参数配置 -->
        <div v-if="currentStep === 1" class="step-content">
          <h3>配置策略参数</h3>
          <div v-if="selectedTemplate && templateSchema">
            <el-form :model="strategyForm.parameters" label-width="150px">
              <el-form-item
                v-for="param in templateSchema.parameters"
                :key="param.name"
                :label="param.description || param.name"
                :required="param.required"
              >
                <!-- 整数输入 -->
                <el-input-number
                  v-if="param.type === 'integer'"
                  v-model="strategyForm.parameters[param.name]"
                  :min="param.min"
                  :max="param.max"
                  :placeholder="param.default?.toString()"
                />
                <!-- 浮点数输入 -->
                <el-input-number
                  v-else-if="param.type === 'float'"
                  v-model="strategyForm.parameters[param.name]"
                  :min="param.min"
                  :max="param.max"
                  :precision="2"
                  :step="0.01"
                  :placeholder="param.default?.toString()"
                />
                <!-- 布尔值输入 -->
                <el-switch
                  v-else-if="param.type === 'boolean'"
                  v-model="strategyForm.parameters[param.name]"
                />
                <!-- 枚举选择 -->
                <el-select
                  v-else-if="param.type === 'enum'"
                  v-model="strategyForm.parameters[param.name]"
                  :placeholder="param.default"
                >
                  <el-option
                    v-for="option in param.options"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
                <!-- 字符串输入 -->
                <el-input
                  v-else
                  v-model="strategyForm.parameters[param.name]"
                  :placeholder="param.default"
                />
              </el-form-item>
            </el-form>
          </div>
          <div v-else>
            <el-alert
              title="自定义策略"
              description="您选择了自定义策略，将在下一步直接编写代码"
              type="info"
              show-icon
              :closable="false"
            />
          </div>
        </div>

        <!-- 步骤3: 代码编辑 -->
        <div v-if="currentStep === 2" class="step-content">
          <h3>编写策略代码</h3>
          <div class="code-editor-container">
            <div class="editor-toolbar">
              <el-button-group>
                <el-button size="small" @click="generateCode" :disabled="!selectedTemplate">
                  <el-icon><Magic /></el-icon>
                  生成代码
                </el-button>
                <el-button size="small" @click="formatCode">
                  <el-icon><Document /></el-icon>
                  格式化
                </el-button>
                <el-button size="small" @click="validateCode">
                  <el-icon><Check /></el-icon>
                  验证代码
                </el-button>
              </el-button-group>
            </div>
            <div class="code-editor">
              <el-input
                v-model="strategyForm.code_content"
                type="textarea"
                :rows="20"
                placeholder="请输入策略代码..."
                class="code-textarea"
              />
            </div>
          </div>
        </div>

        <!-- 步骤4: 验证测试 -->
        <div v-if="currentStep === 3" class="step-content">
          <h3>验证策略代码</h3>
          <div class="validation-panel">
            <el-button type="primary" @click="runValidation" :loading="validating" icon="el-icon-video-play">
              运行验证
            </el-button>

            <div v-if="validationResult" class="validation-result">
              <el-alert
                :title="validationResult.valid ? '验证通过' : '验证失败'"
                :type="validationResult.valid ? 'success' : 'error'"
                show-icon
                :closable="false"
              />

              <div v-if="validationResult.errors && validationResult.errors.length" class="errors">
                <h4>错误信息：</h4>
                <ul>
                  <li v-for="error in validationResult.errors" :key="error" class="error-item">
                    {{ error }}
                  </li>
                </ul>
              </div>

              <div v-if="validationResult.warnings && validationResult.warnings.length" class="warnings">
                <h4>警告信息：</h4>
                <ul>
                  <li v-for="warning in validationResult.warnings" :key="warning" class="warning-item">
                    {{ warning }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤5: 保存部署 -->
        <div v-if="currentStep === 4" class="step-content">
          <h3>保存并部署策略</h3>
          <div class="summary-panel">
            <el-descriptions title="策略信息" :column="2" border>
              <el-descriptions-item label="策略名称">{{ strategyForm.name }}</el-descriptions-item>
              <el-descriptions-item label="策略类型">{{ getTypeLabel(strategyForm.type) }}</el-descriptions-item>
              <el-descriptions-item label="策略分类">{{ getCategoryLabel(strategyForm.category) }}</el-descriptions-item>
              <el-descriptions-item label="代码类型">{{ strategyForm.code_type }}</el-descriptions-item>
              <el-descriptions-item label="策略描述" :span="2">{{ strategyForm.description }}</el-descriptions-item>
            </el-descriptions>

            <div class="deploy-options">
              <el-form :model="deployForm" label-width="120px">
                <el-form-item label="交易对">
                  <el-input v-model="deployForm.symbol" placeholder="如: BTCUSDT" />
                </el-form-item>
                <el-form-item label="时间周期">
                  <el-select v-model="deployForm.timeframe" placeholder="请选择时间周期">
                    <el-option label="1分钟" value="1m" />
                    <el-option label="5分钟" value="5m" />
                    <el-option label="15分钟" value="15m" />
                    <el-option label="30分钟" value="30m" />
                    <el-option label="1小时" value="1h" />
                    <el-option label="4小时" value="4h" />
                    <el-option label="1天" value="1d" />
                  </el-select>
                </el-form-item>
                <el-form-item label="立即激活">
                  <el-switch v-model="deployForm.is_active" />
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="wizard-actions">
        <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
        <el-button
          v-if="currentStep < 4"
          type="primary"
          @click="nextStep"
          :disabled="!canProceed"
        >
          下一步
        </el-button>
        <el-button
          v-if="currentStep === 4"
          type="success"
          @click="saveStrategy"
          :loading="saving"
        >
          保存策略
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import { Message, MessageBox } from 'element-ui'
import strategyApi from '@/api/strategy'

export default {
  name: 'StrategyWizard',
  data() {
    return {
      // 响应式数据
      currentStep: 0,
      selectedTemplate: null,
      validating: false,
      saving: false,
      strategyTypes: [],
      availableTemplates: [],
      templateSchema: null,
      validationResult: null,

      // 表单数据
      strategyForm: {
        name: '',
        description: '',
        type: '',
        category: '',
        code_type: 'python',
        code_content: '',
        parameters: {},
        template_id: null
      },

      deployForm: {
        symbol: '',
        timeframe: '1h',
        is_active: false
      }
    }
  },
  computed: {
    // 计算属性
    currentCategories() {
      const type = this.strategyTypes.find(t => t.value === this.strategyForm.type)
      return type ? type.categories : []
    },

    canProceed() {
      switch (this.currentStep) {
        case 0:
          return this.strategyForm.name && this.strategyForm.type && this.strategyForm.category
        case 1:
          return true // 参数配置可以为空
        case 2:
          return this.strategyForm.code_content.trim().length > 0
        case 3:
          return this.validationResult && this.validationResult.valid
        default:
          return true
      }
    }
  },
  methods: {
    // 方法
    async loadStrategyTypes() {
      try {
        const response = await strategyApi.getStrategyTypes()
        this.strategyTypes = response.data
      } catch (error) {
        Message.error('加载策略类型失败')
      }
    },

    async loadTemplates() {
      if (!this.strategyForm.type) return

      try {
        const params = {
          type: this.strategyForm.type
        }

        // 只有选择了分类才添加分类过滤
        if (this.strategyForm.category) {
          params.category = this.strategyForm.category
        }

        const response = await strategyApi.getTemplates(params)
        this.availableTemplates = response.data || []
        console.log('加载到的模板:', this.availableTemplates)
      } catch (error) {
        console.error('加载策略模板失败:', error)
        Message.error('加载策略模板失败')
      }
    },

    onTypeChange() {
      this.strategyForm.category = ''
      this.selectedTemplate = null
      this.availableTemplates = []
      this.loadTemplates()
    },

    onCategoryChange() {
      this.selectedTemplate = null
      this.loadTemplates()
    },

    async generateCode() {
      if (!this.selectedTemplate) return

      try {
        const response = await strategyApi.generateCode(this.selectedTemplate, this.strategyForm.parameters)
        if (response.success) {
          this.strategyForm.code_content = response.code
          Message.success('代码生成成功')
        } else {
          Message.error(response.message || '代码生成失败')
        }
      } catch (error) {
        Message.error('代码生成失败')
      }
    },

    async validateCode() {
      if (!this.strategyForm.code_content.trim()) {
        Message.warning('请先输入策略代码')
        return
      }

      this.validating = true
      try {
        const response = await strategyApi.validateCode({
          code: this.strategyForm.code_content,
          code_type: this.strategyForm.code_type
        })
        this.validationResult = response

        if (response.valid) {
          Message.success('代码验证通过')
        } else {
          Message.error('代码验证失败')
        }
      } catch (error) {
        Message.error('代码验证失败')
      } finally {
        this.validating = false
      }
    },

    runValidation() {
      this.validateCode()
    },

    formatCode() {
      // 简单的代码格式化
      if (this.strategyForm.code_content) {
        // 这里可以集成代码格式化库
        Message.success('代码格式化完成')
      }
    },

    async nextStep() {
      if (this.currentStep === 0 && this.selectedTemplate) {
        // 加载模板参数
        try {
          const template = this.availableTemplates.find(t => t.id === this.selectedTemplate)
          if (template) {
            this.templateSchema = template.parameter_schema
            this.strategyForm.template_id = template.id
            // 设置默认参数值
            if (template.default_parameters) {
              Object.assign(this.strategyForm.parameters, template.default_parameters)
            }
          }
        } catch (error) {
          Message.error('加载模板参数失败')
          return
        }
      }

      if (this.currentStep === 1 && this.selectedTemplate) {
        // 自动生成代码
        await this.generateCode()
      }

      this.currentStep++
    },

    prevStep() {
      this.currentStep--
    },

    async saveStrategy() {
      this.saving = true
      try {
        const strategyData = {
          ...this.strategyForm,
          ...this.deployForm
        }

        const response = await strategyApi.createStrategy(strategyData)
        if (response.success) {
          Message.success('策略创建成功')
          this.$router.push('/strategy/management')
        } else {
          Message.error(response.message || '策略创建失败')
        }
      } catch (error) {
        Message.error('策略创建失败')
      } finally {
        this.saving = false
      }
    },

    getTypeLabel(value) {
      const type = this.strategyTypes.find(t => t.value === value)
      return type ? type.label : value
    },

    getCategoryLabel(value) {
      const category = this.currentCategories.find(c => c.value === value)
      return category ? category.label : value
    }
  },

  // 生命周期
  mounted() {
    this.loadStrategyTypes()
  }
}
</script>

<style scoped>
.strategy-wizard {
  padding: 20px;
}

.wizard-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.wizard-steps {
  margin: 30px 0;
}

.wizard-content {
  min-height: 400px;
  padding: 20px 0;
}

.step-content h3 {
  margin-bottom: 20px;
  color: #303133;
}

.template-selection {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 20px;
  height: 400px;
  overflow-y: auto;
}

.template-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.template-item {
  display: block !important;
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  transition: all 0.3s;
}

.template-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.template-info {
  margin-left: 20px;
}

.template-name {
  font-weight: bold;
  color: #303133;
}

.template-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.code-editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.editor-toolbar {
  padding: 10px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #f5f7fa;
}

.code-editor {
  padding: 10px;
}

.code-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.validation-panel {
  padding: 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.validation-result {
  margin-top: 20px;
}

.errors, .warnings {
  margin-top: 15px;
}

.error-item {
  color: #f56c6c;
  margin: 5px 0;
}

.warning-item {
  color: #e6a23c;
  margin: 5px 0;
}

.summary-panel {
  padding: 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.deploy-options {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.wizard-actions {
  margin-top: 30px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.wizard-actions .el-button {
  margin: 0 10px;
}
</style>

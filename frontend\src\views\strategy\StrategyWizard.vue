<template>
  <div class="page quantum-page">
    <!-- 页面标题 -->
    <h2 class="page-title quantum-title">策略创建向导</h2>

    <!-- 返回按钮 -->
    <div class="page-header">
      <button class="quantum-button back-button" @click="$router.go(-1)">
        <i class="el-icon-arrow-left"></i>
        返回
      </button>
    </div>

    <!-- 步骤指示器 -->
    <div class="dashboard-section glow-panel">
      <h3 class="section-title neon-blue">策略创建向导</h3>
      <div class="wizard-steps quantum-steps">
        <div
          v-for="(step, index) in steps"
          :key="index"
          class="step-item"
          :class="{
            'active': index === currentStep,
            'completed': index < currentStep,
            'disabled': index > currentStep
          }"
        >
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-content">
            <div class="step-title">{{ step.title }}</div>
            <div class="step-description">{{ step.description }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 步骤内容 -->
    <div class="wizard-content">
      <!-- 步骤1: 策略类型选择 -->
      <div v-if="currentStep === 0" class="dashboard-section glow-panel">
        <h3 class="section-title neon-pink">选择策略类型</h3>
        <div class="step-content">
          <div class="form-grid">
            <div class="form-section">
              <div class="control-group">
                <label class="control-label neon-blue">策略名称 *</label>
                <input
                  v-model="strategyForm.name"
                  class="quantum-input"
                  placeholder="请输入策略名称"
                  required
                />
              </div>

              <div class="control-group">
                <label class="control-label neon-blue">策略描述</label>
                <textarea
                  v-model="strategyForm.description"
                  class="quantum-textarea"
                  rows="3"
                  placeholder="请输入策略描述"
                ></textarea>
              </div>

              <div class="control-group">
                <label class="control-label neon-blue">策略类型 *</label>
                <select
                  v-model="strategyForm.type"
                  class="quantum-dropdown"
                  @change="onTypeChange"
                  required
                >
                  <option value="">请选择策略类型</option>
                  <option
                    v-for="type in strategyTypes"
                    :key="type.value"
                    :value="type.value"
                  >
                    {{ type.label }} - {{ type.description }}
                  </option>
                </select>
              </div>

              <div class="control-group" v-if="strategyForm.type">
                <label class="control-label neon-blue">策略分类 *</label>
                <select
                  v-model="strategyForm.category"
                  class="quantum-dropdown"
                  @change="onCategoryChange"
                  required
                >
                  <option value="">请选择策略分类</option>
                  <option
                    v-for="category in currentCategories"
                    :key="category.value"
                    :value="category.value"
                  >
                    {{ category.label }}
                  </option>
                </select>
              </div>

              <div class="control-group">
                <label class="control-label neon-blue">代码类型</label>
                <div class="radio-group">
                  <label class="quantum-radio">
                    <input type="radio" v-model="strategyForm.code_type" value="python" />
                    <span class="radio-label">Python</span>
                  </label>
                  <label class="quantum-radio">
                    <input type="radio" v-model="strategyForm.code_type" value="pinescript" />
                    <span class="radio-label">Pine Script</span>
                  </label>
                </div>
              </div>
            </div>

            <div class="template-section">
              <h4 class="section-title neon-blue">选择策略模板</h4>
              <div class="template-selection quantum-panel">
                <div class="template-list">
                  <label
                    v-for="template in availableTemplates"
                    :key="template.id"
                    class="template-item quantum-radio"
                  >
                    <input
                      type="radio"
                      :value="template.id"
                      v-model="selectedTemplate"
                    />
                    <div class="template-info">
                      <div class="template-name neon-value">{{ template.name }}</div>
                      <div class="template-desc">{{ template.description }}</div>
                    </div>
                  </label>

                  <label class="template-item quantum-radio">
                    <input
                      type="radio"
                      :value="null"
                      v-model="selectedTemplate"
                    />
                    <div class="template-info">
                      <div class="template-name neon-value">自定义策略</div>
                      <div class="template-desc">从空白模板开始创建</div>
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤2: 参数配置 -->
      <div v-if="currentStep === 1" class="dashboard-section glow-panel">
        <h3 class="section-title neon-pink">配置策略参数</h3>
        <div class="step-content">
          <!-- 策略参数配置 -->
          <div class="quantum-tabs">
            <div class="tab-headers">
              <button
                class="tab-header quantum-button"
                :class="{ active: parameterTab === 'strategy' }"
                @click="parameterTab = 'strategy'"
              >
                策略参数
              </button>
              <button
                class="tab-header quantum-button"
                :class="{ active: parameterTab === 'risk' }"
                @click="parameterTab = 'risk'"
              >
                风险管理
              </button>
              <button
                class="tab-header quantum-button"
                :class="{ active: parameterTab === 'timing' }"
                @click="parameterTab = 'timing'"
              >
                交易时间
              </button>
            </div>

            <!-- 策略参数 -->
            <div v-if="parameterTab === 'strategy'" class="tab-content">
              <div v-if="selectedTemplate && templateSchema" class="parameter-form">
                <div
                  v-for="param in templateSchema.parameters"
                  :key="param.name"
                  class="control-group"
                >
                  <label class="control-label neon-blue">
                    {{ param.description || param.name }}
                    <span v-if="param.required" class="required">*</span>
                  </label>

                  <!-- 整数输入 -->
                  <input
                    v-if="param.type === 'integer'"
                    v-model.number="strategyForm.parameters[param.name]"
                    type="number"
                    class="quantum-input"
                    :min="param.min"
                    :max="param.max"
                    :placeholder="param.default?.toString()"
                  />

                  <!-- 浮点数输入 -->
                  <input
                    v-else-if="param.type === 'float'"
                    v-model.number="strategyForm.parameters[param.name]"
                    type="number"
                    step="0.01"
                    class="quantum-input"
                    :min="param.min"
                    :max="param.max"
                    :placeholder="param.default?.toString()"
                  />

                  <!-- 布尔值输入 -->
                  <label v-else-if="param.type === 'boolean'" class="quantum-switch">
                    <input
                      type="checkbox"
                      v-model="strategyForm.parameters[param.name]"
                    />
                    <span class="switch-slider"></span>
                  </label>

                  <!-- 枚举选择 -->
                  <select
                    v-else-if="param.type === 'enum'"
                    v-model="strategyForm.parameters[param.name]"
                    class="quantum-dropdown"
                  >
                    <option :value="param.default">{{ param.default }}</option>
                    <option
                      v-for="option in param.options"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </option>
                  </select>

                  <!-- 字符串输入 -->
                  <input
                    v-else
                    v-model="strategyForm.parameters[param.name]"
                    class="quantum-input"
                    :placeholder="param.default"
                  />
                </div>
              </div>
              <div v-else class="info-panel">
                <div class="info-message neon-blue">
                  <i class="el-icon-info"></i>
                  您选择了自定义策略，请在风险管理标签页配置风险参数
                </div>
              </div>
            </div>

            <!-- 风险管理参数 -->
            <div v-if="parameterTab === 'risk'" class="tab-content">
              <div class="risk-form">
                <div class="form-grid">
                  <div class="form-section">
                    <h4 class="section-title neon-blue">止损止盈设置</h4>

                    <div class="control-group">
                      <label class="control-label neon-blue">启用止损</label>
                      <label class="quantum-switch">
                        <input type="checkbox" v-model="strategyForm.risk_management.enable_stop_loss" />
                        <span class="switch-slider"></span>
                      </label>
                    </div>

                    <div v-if="strategyForm.risk_management.enable_stop_loss" class="control-group">
                      <label class="control-label neon-blue">止损比例(%)</label>
                      <input
                        v-model.number="strategyForm.risk_management.stop_loss_pct"
                        type="number"
                        step="0.1"
                        min="0.1"
                        max="50"
                        class="quantum-input"
                        placeholder="2.0"
                      />
                      <span class="param-hint">建议设置1-5%，过小容易被噪音触发</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 交易时间设置 -->
            <div v-if="parameterTab === 'timing'" class="tab-content">
              <div class="timing-form">
                <div class="control-group">
                  <label class="control-label neon-blue">启用交易时间限制</label>
                  <label class="quantum-switch">
                    <input type="checkbox" v-model="strategyForm.trading_schedule.enable_schedule" />
                    <span class="switch-slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="wizard-actions dashboard-section glow-panel">
      <div class="action-buttons">
        <button
          v-if="currentStep > 0"
          class="quantum-button secondary"
          @click="prevStep"
        >
          上一步
        </button>
        <button
          v-if="currentStep < 4"
          class="quantum-button primary"
          @click="nextStep"
          :disabled="!canProceed"
        >
          下一步
        </button>
        <button
          v-if="currentStep === 4"
          class="quantum-button success"
          @click="saveStrategy"
          :disabled="saving"
        >
          {{ saving ? '保存中...' : '保存策略' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { Message } from 'element-ui'
import strategyApi from '@/api/strategy'
import dataAPI from '@/api/data'

export default {
  name: 'StrategyWizardNew',
  data() {
    return {
      // 响应式数据
      currentStep: 0,
      selectedTemplate: null,
      validating: false,
      saving: false,
      strategyTypes: [],
      availableTemplates: [],
      templateSchema: null,
      validationResult: null,
      availableSymbols: [],
      loadingSymbols: false,
      parameterTab: 'strategy',

      // 步骤定义
      steps: [
        {
          title: '选择策略类型',
          description: '选择策略分类和模板'
        },
        {
          title: '配置参数',
          description: '设置策略参数'
        },
        {
          title: '编写代码',
          description: '编辑策略代码'
        },
        {
          title: '验证测试',
          description: '验证策略代码'
        },
        {
          title: '保存部署',
          description: '保存并部署策略'
        }
      ],

      // 表单数据
      strategyForm: {
        name: '',
        description: '',
        type: '',
        category: '',
        code_type: 'python',
        code_content: '',
        parameters: {},
        template_id: null,

        // 风险管理参数
        risk_management: {
          enable_stop_loss: true,
          stop_loss_pct: 2.0,
          enable_take_profit: true,
          take_profit_pct: 5.0,
          enable_trailing_stop: false,
          trailing_stop_pct: 1.5,
          position_sizing_mode: 'fixed',
          max_position_pct: 20,
          risk_per_trade_pct: 1.0,
          max_drawdown_pct: 15,
          max_trades_per_day: 10,
          max_consecutive_losses: 5
        },

        // 交易时间设置
        trading_schedule: {
          enable_schedule: false,
          trading_days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
          start_time: '09:00',
          end_time: '17:00',
          avoid_news_events: true
        }
      },

      deployForm: {
        symbol: '',
        timeframe: '1h',
        is_active: false
      }
    }
  },
  computed: {
    // 计算属性
    currentCategories() {
      const type = this.strategyTypes.find(t => t.value === this.strategyForm.type)
      return type ? type.categories : []
    },

    canProceed() {
      switch (this.currentStep) {
        case 0:
          return this.strategyForm.name && this.strategyForm.type && this.strategyForm.category
        case 1:
          return true // 参数配置可以为空
        case 2:
          return this.strategyForm.code_content.trim().length > 0
        case 3:
          return this.validationResult && this.validationResult.valid
        default:
          return true
      }
    }
  },
  methods: {
    // 方法
    async loadStrategyTypes() {
      try {
        const response = await strategyApi.getStrategyTypes()
        this.strategyTypes = response.data
      } catch (error) {
        Message.error('加载策略类型失败')
      }
    },

    async loadTemplates() {
      if (!this.strategyForm.type) return

      try {
        const params = {
          type: this.strategyForm.type
        }

        // 只有选择了分类才添加分类过滤
        if (this.strategyForm.category) {
          params.category = this.strategyForm.category
        }

        const response = await strategyApi.getTemplates(params)
        this.availableTemplates = response.data || []
        console.log('加载到的模板:', this.availableTemplates)
      } catch (error) {
        console.error('加载策略模板失败:', error)
        Message.error('加载策略模板失败')
      }
    },

    onTypeChange() {
      this.strategyForm.category = ''
      this.selectedTemplate = null
      this.availableTemplates = []
      this.loadTemplates()
    },

    onCategoryChange() {
      this.selectedTemplate = null
      this.loadTemplates()
    },

    async nextStep() {
      if (this.currentStep === 0 && this.selectedTemplate) {
        // 加载模板参数
        try {
          const template = this.availableTemplates.find(t => t.id === this.selectedTemplate)
          if (template) {
            this.templateSchema = template.parameter_schema
            this.strategyForm.template_id = template.id
            // 设置默认参数值
            if (template.default_parameters) {
              Object.assign(this.strategyForm.parameters, template.default_parameters)
            }
          }
        } catch (error) {
          Message.error('加载模板参数失败')
          return
        }
      }

      this.currentStep++
    },

    prevStep() {
      this.currentStep--
    },

    async saveStrategy() {
      this.saving = true
      try {
        const strategyData = {
          ...this.strategyForm,
          ...this.deployForm
        }

        const response = await strategyApi.createStrategy(strategyData)
        if (response.success) {
          Message.success('策略创建成功')
          this.$router.push('/strategy/management')
        } else {
          Message.error(response.message || '策略创建失败')
        }
      } catch (error) {
        Message.error('策略创建失败')
      } finally {
        this.saving = false
      }
    }
  },

  // 生命周期
  mounted() {
    this.loadStrategyTypes()
  }
}
</script>

<style scoped>
/* 量子科技美学风格 - 策略创建向导 */

/* 页面基础样式 */
.page.quantum-page {
  padding: 150px 20px 20px 20px; /* 顶部间距适应固定导航栏 */
  background: linear-gradient(135deg, rgba(8,20,40,0.95) 0%, rgba(0,10,30,0.98) 100%);
  min-height: 100vh;
  color: #FFFFFF;
  font-family: "Orbitron", "Rajdhani", sans-serif;
}

/* 页面标题 */
.page-title.quantum-title {
  font-size: 28px;
  font-weight: 600;
  color: #00F7FF;
  text-align: center;
  margin-bottom: 30px;
  text-shadow: 0 0 15px rgba(0,247,255,0.6);
  animation: titleGlow 2s infinite alternate;
}

@keyframes titleGlow {
  from { text-shadow: 0 0 15px rgba(0,247,255,0.6); }
  to { text-shadow: 0 0 25px rgba(0,247,255,0.9); }
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.back-button {
  background: rgba(6,21,46,0.9);
  color: #00F7FF;
  border: 1px solid rgba(0,247,255,0.5);
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
}

.back-button:hover {
  background: rgba(0,247,255,0.15);
  box-shadow: 0 0 15px rgba(0,247,255,0.4);
  transform: translateY(-2px);
}

/* 发光面板 */
.dashboard-section.glow-panel {
  background: rgba(6,21,46,0.7);
  border-radius: 10px;
  border: 1px solid rgba(0,247,255,0.3);
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 0 15px rgba(0,247,255,0.2);
  animation: borderGlow 3s infinite alternate;
}

@keyframes borderGlow {
  from {
    border-color: rgba(0,247,255,0.3);
    box-shadow: 0 0 15px rgba(0,247,255,0.2);
  }
  to {
    border-color: rgba(0,247,255,0.6);
    box-shadow: 0 0 25px rgba(0,247,255,0.4);
  }
}

/* 章节标题 */
.section-title.neon-blue {
  color: #00F7FF;
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 20px;
  text-shadow: 0 0 10px rgba(0,247,255,0.8);
  border-bottom: 2px solid rgba(0,247,255,0.5);
  padding-bottom: 10px;
}

.section-title.neon-pink {
  color: #FF00F7;
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 20px;
  text-shadow: 0 0 10px rgba(255,0,247,0.8);
  border-bottom: 2px solid rgba(255,0,247,0.5);
  padding-bottom: 10px;
}

/* 步骤指示器 */
.wizard-steps.quantum-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
  position: relative;
}

.step-item {
  display: flex;
  align-items: center;
  flex: 1;
  position: relative;
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 20px;
  right: -50%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, rgba(0,247,255,0.5) 0%, rgba(0,247,255,0.1) 100%);
  z-index: 1;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(6,21,46,0.9);
  border: 2px solid rgba(0,247,255,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00F7FF;
  font-weight: bold;
  margin-right: 15px;
  z-index: 2;
  position: relative;
  transition: all 0.3s ease;
}

.step-item.active .step-number {
  background: rgba(0,247,255,0.2);
  border-color: #00F7FF;
  box-shadow: 0 0 15px rgba(0,247,255,0.5);
  animation: pulse 2s infinite;
}

.step-item.completed .step-number {
  background: rgba(0,247,255,0.3);
  border-color: #00F7FF;
  color: #FFFFFF;
}

@keyframes pulse {
  0% { box-shadow: 0 0 15px rgba(0,247,255,0.5); }
  50% { box-shadow: 0 0 25px rgba(0,247,255,0.8); }
  100% { box-shadow: 0 0 15px rgba(0,247,255,0.5); }
}

.step-title {
  color: #00F7FF;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 5px;
}

.step-description {
  color: rgba(255,255,255,0.7);
  font-size: 14px;
}

.step-item.active .step-title {
  color: #FFFFFF;
  text-shadow: 0 0 8px rgba(0,247,255,0.6);
}

/* 表单网格布局 */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  align-items: start;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 控件组 */
.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-label.neon-blue {
  color: #00F7FF;
  font-weight: 500;
  font-size: 14px;
  text-shadow: 0 0 8px rgba(0,247,255,0.8);
}

.required {
  color: #FF006F;
  margin-left: 4px;
}

/* 量子输入框 */
.quantum-input, .quantum-textarea, .quantum-dropdown {
  background: rgba(6,21,46,0.8);
  border: 1px solid rgba(0,247,255,0.3);
  border-radius: 6px;
  padding: 12px 15px;
  color: #00F7FF;
  font-family: inherit;
  font-size: 14px;
  transition: all 0.3s ease;
}

.quantum-input:focus, .quantum-textarea:focus, .quantum-dropdown:focus {
  outline: none;
  border-color: #00F7FF;
  box-shadow: 0 0 10px rgba(0,247,255,0.4);
  background: rgba(6,21,46,0.9);
}

.quantum-input::placeholder, .quantum-textarea::placeholder {
  color: rgba(0,247,255,0.5);
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  gap: 20px;
}

.quantum-radio {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #00F7FF;
  font-size: 14px;
}

.quantum-radio input[type="radio"] {
  margin-right: 8px;
  accent-color: #00F7FF;
}

.radio-label {
  color: #00F7FF;
  text-shadow: 0 0 5px rgba(0,247,255,0.6);
}

/* 模板选择区域 */
.template-section {
  background: rgba(0,10,30,0.5);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid rgba(0,247,255,0.2);
}

.template-selection.quantum-panel {
  max-height: 400px;
  overflow-y: auto;
  padding: 15px;
  background: rgba(6,21,46,0.6);
  border-radius: 6px;
  border: 1px solid rgba(0,247,255,0.2);
}

.template-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.template-item.quantum-radio {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  background: rgba(6,21,46,0.7);
  border: 1px solid rgba(0,247,255,0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-item.quantum-radio:hover {
  border-color: rgba(0,247,255,0.5);
  background: rgba(0,247,255,0.1);
  box-shadow: 0 0 10px rgba(0,247,255,0.2);
}

.template-item.quantum-radio input[type="radio"] {
  margin-right: 12px;
  margin-top: 2px;
}

.template-info {
  flex: 1;
}

.template-name.neon-value {
  color: #00F7FF;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 5px;
  text-shadow: 0 0 8px rgba(0,247,255,0.6);
}

.template-desc {
  color: rgba(255,255,255,0.7);
  font-size: 13px;
  line-height: 1.4;
}

/* 量子标签页 */
.quantum-tabs {
  margin-top: 20px;
}

.tab-headers {
  display: flex;
  gap: 10px;
  margin-bottom: 25px;
  border-bottom: 1px solid rgba(0,247,255,0.2);
  padding-bottom: 15px;
}

.tab-header.quantum-button {
  background: rgba(6,21,46,0.7);
  color: rgba(0,247,255,0.7);
  border: 1px solid rgba(0,247,255,0.2);
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
  font-size: 14px;
}

.tab-header.quantum-button:hover {
  color: #00F7FF;
  border-color: rgba(0,247,255,0.5);
  background: rgba(0,247,255,0.1);
}

.tab-header.quantum-button.active {
  background: rgba(0,247,255,0.2);
  color: #FFFFFF;
  border-color: #00F7FF;
  box-shadow: 0 0 10px rgba(0,247,255,0.4);
}

.tab-content {
  padding: 20px;
  background: rgba(0,10,30,0.3);
  border-radius: 8px;
  border: 1px solid rgba(0,247,255,0.1);
}

/* 参数表单 */
.parameter-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

/* 信息面板 */
.info-panel {
  text-align: center;
  padding: 40px 20px;
}

.info-message.neon-blue {
  color: #00F7FF;
  font-size: 16px;
  text-shadow: 0 0 8px rgba(0,247,255,0.6);
}

.info-message i {
  margin-right: 10px;
  font-size: 18px;
}

/* 量子开关 */
.quantum-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.quantum-switch input[type="checkbox"] {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(6,21,46,0.8);
  border: 1px solid rgba(0,247,255,0.3);
  transition: 0.3s;
  border-radius: 24px;
}

.switch-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 2px;
  bottom: 2px;
  background-color: rgba(0,247,255,0.7);
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(0,247,255,0.5);
}

input[type="checkbox"]:checked + .switch-slider {
  background-color: rgba(0,247,255,0.3);
  border-color: #00F7FF;
}

input[type="checkbox"]:checked + .switch-slider:before {
  transform: translateX(24px);
  background-color: #00F7FF;
  box-shadow: 0 0 10px rgba(0,247,255,0.8);
}

/* 参数提示 */
.param-hint {
  color: rgba(255,255,255,0.5);
  font-size: 12px;
  font-style: italic;
  margin-top: 5px;
}

/* 操作按钮区域 */
.wizard-actions {
  margin-top: 30px;
  text-align: center;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.quantum-button {
  background: rgba(6,21,46,0.9);
  color: #00F7FF;
  border: 1px solid rgba(0,247,255,0.5);
  padding: 12px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-family: inherit;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.quantum-button:hover:not(:disabled) {
  background: rgba(0,247,255,0.15);
  box-shadow: 0 0 15px rgba(0,247,255,0.4);
  transform: translateY(-2px);
}

.quantum-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 0 8px rgba(0,247,255,0.3);
}

.quantum-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(6,21,46,0.5);
  color: rgba(0,247,255,0.5);
}

.quantum-button.primary {
  background: rgba(0,247,255,0.2);
  color: #FFFFFF;
  border-color: #00F7FF;
  box-shadow: 0 0 10px rgba(0,247,255,0.3);
}

.quantum-button.primary:hover:not(:disabled) {
  background: rgba(0,247,255,0.3);
  box-shadow: 0 0 20px rgba(0,247,255,0.6);
}

.quantum-button.secondary {
  background: rgba(6,21,46,0.7);
  color: rgba(0,247,255,0.8);
  border-color: rgba(0,247,255,0.3);
}

.quantum-button.success {
  background: rgba(0,255,148,0.2);
  color: #FFFFFF;
  border-color: #00FF94;
  box-shadow: 0 0 10px rgba(0,255,148,0.3);
}

.quantum-button.success:hover:not(:disabled) {
  background: rgba(0,255,148,0.3);
  box-shadow: 0 0 20px rgba(0,255,148,0.6);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .wizard-steps.quantum-steps {
    flex-direction: column;
    gap: 15px;
  }

  .step-item:not(:last-child)::after {
    display: none;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .quantum-button {
    width: 200px;
  }
}

/* 滚动条样式 */
.template-selection::-webkit-scrollbar {
  width: 6px;
}

.template-selection::-webkit-scrollbar-track {
  background: rgba(6,21,46,0.5);
  border-radius: 3px;
}

.template-selection::-webkit-scrollbar-thumb {
  background: rgba(0,247,255,0.5);
  border-radius: 3px;
}

.template-selection::-webkit-scrollbar-thumb:hover {
  background: rgba(0,247,255,0.7);
}
</style>

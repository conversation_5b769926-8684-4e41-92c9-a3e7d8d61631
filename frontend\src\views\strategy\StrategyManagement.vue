<template>
  <div class="strategy-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>策略管理</h1>
        <p>管理和监控您的交易策略</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="createStrategy" icon="el-icon-plus">
          创建策略
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total || 0 }}</div>
            <div class="stat-label">总策略数</div>
          </div>
          <el-icon class="stat-icon"><Document /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.active || 0 }}</div>
            <div class="stat-label">运行中</div>
          </div>
          <el-icon class="stat-icon active"><Play /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.inactive || 0 }}</div>
            <div class="stat-label">已停止</div>
          </div>
          <el-icon class="stat-icon inactive"><VideoPause /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ todayCreated }}</div>
            <div class="stat-label">今日新增</div>
          </div>
          <i class="el-icon-plus stat-icon new"></i>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选和搜索 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="策略类型">
          <el-select v-model="filterForm.type" placeholder="全部类型" clearable @change="loadStrategies">
            <el-option
              v-for="type in strategyTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filterForm.status" placeholder="全部状态" clearable @change="loadStrategies">
            <el-option label="已创建" value="created" />
            <el-option label="已验证" value="validated" />
            <el-option label="运行中" value="active" />
            <el-option label="已停止" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item label="搜索">
          <el-input
            v-model="filterForm.search"
            placeholder="搜索策略名称"
            @input="debounceSearch"
            prefix-icon="el-icon-search"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 策略列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="strategies"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="策略名称" min-width="150">
          <template #default="{ row }">
            <div class="strategy-name">
              <span class="name">{{ row.name }}</span>
              <el-tag v-if="row.template_id" size="small" type="info">模板</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="分类" width="120">
          <template #default="{ row }">
            {{ getCategoryLabel(row.category) }}
          </template>
        </el-table-column>
        <el-table-column prop="code_type" label="代码类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.code_type === 'python' ? 'success' : 'warning'" size="small">
              {{ row.code_type === 'python' ? 'Python' : 'Pine Script' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="symbol" label="交易对" width="100" />
        <el-table-column prop="timeframe" label="时间周期" width="100" />
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" icon="el-icon-view" @click="viewStrategy(row)">
              </el-button>
              <el-button size="small" icon="el-icon-edit" @click="editStrategy(row)">
              </el-button>
              <el-button size="small" icon="el-icon-copy-document" @click="copyStrategy(row)">
              </el-button>
              <el-button
                size="small"
                type="danger"
                icon="el-icon-delete"
                @click="deleteStrategy(row)"
              >
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadStrategies"
          @current-change="loadStrategies"
        />
      </div>
    </el-card>

    <!-- 批量操作 -->
    <div v-if="selectedStrategies.length > 0" class="batch-actions">
      <el-card>
        <div class="batch-content">
          <span>已选择 {{ selectedStrategies.length }} 个策略</span>
          <div class="batch-buttons">
            <el-button @click="batchActivate">批量激活</el-button>
            <el-button @click="batchDeactivate">批量停止</el-button>
            <el-button type="danger" @click="batchDelete">批量删除</el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Message, MessageBox } from 'element-ui'
import strategyApi from '@/api/strategy'

export default {
  name: 'StrategyManagement',
  setup() {
    const router = useRouter()

    // 响应式数据
    const loading = ref(false)
    const strategies = ref([])
    const selectedStrategies = ref([])
    const strategyTypes = ref([])
    const stats = ref({})

    // 筛选表单
    const filterForm = reactive({
      type: '',
      status: '',
      search: ''
    })

    // 分页
    const pagination = reactive({
      page: 1,
      size: 20,
      total: 0
    })

    // 计算属性
    const todayCreated = computed(() => {
      const today = new Date().toDateString()
      return strategies.value.filter(s =>
        new Date(s.created_at).toDateString() === today
      ).length
    })

    // 防抖搜索
    let debounceTimer = null
    const debounceSearch = () => {
      clearTimeout(debounceTimer)
      debounceTimer = setTimeout(() => {
        pagination.page = 1
        loadStrategies()
      }, 500)
    }

    // 方法
    const loadStrategies = async () => {
      loading.value = true
      try {
        const params = {
          page: pagination.page,
          limit: pagination.size,
          type: filterForm.type || undefined,
          status: filterForm.status || undefined,
          search: filterForm.search || undefined
        }

        const response = await strategyApi.getStrategies(params)
        if (response.success) {
          strategies.value = response.data
          pagination.total = response.total || response.data.length
        }
      } catch (error) {
        Message.error('加载策略列表失败')
      } finally {
        loading.value = false
      }
    }

    const loadStats = async () => {
      try {
        const response = await strategyApi.getStrategyStats()
        if (response.success) {
          stats.value = response.data
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    }

    const loadStrategyTypes = async () => {
      try {
        const response = await strategyApi.getStrategyTypes()
        if (response.success) {
          strategyTypes.value = response.data
        }
      } catch (error) {
        console.error('加载策略类型失败:', error)
      }
    }

    const createStrategy = () => {
      router.push('/strategy/wizard')
    }

    const viewStrategy = (strategy) => {
      router.push(`/strategy/detail/${strategy.id}`)
    }

    const editStrategy = (strategy) => {
      router.push(`/strategy/edit/${strategy.id}`)
    }

    const copyStrategy = async (strategy) => {
      try {
        const response = await strategyApi.getStrategy(strategy.id)
        if (response.success) {
          const copyData = {
            ...response.data,
            name: `${response.data.name} - 副本`,
            id: undefined,
            created_at: undefined,
            updated_at: undefined
          }

          const createResponse = await strategyApi.createStrategy(copyData)
          if (createResponse.success) {
            Message.success('策略复制成功')
            loadStrategies()
          }
        }
      } catch (error) {
        Message.error('策略复制失败')
      }
    }

    const deleteStrategy = async (strategy) => {
      try {
        await MessageBox.confirm(
          `确定要删除策略 "${strategy.name}" 吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        const response = await strategyApi.deleteStrategy(strategy.id)
        if (response.success) {
          Message.success('策略删除成功')
          loadStrategies()
          loadStats()
        }
      } catch (error) {
        if (error !== 'cancel') {
          Message.error('策略删除失败')
        }
      }
    }

    const handleSelectionChange = (selection) => {
      selectedStrategies.value = selection
    }

    const batchActivate = async () => {
      // 批量激活逻辑
      Message.info('批量激活功能开发中')
    }

    const batchDeactivate = async () => {
      // 批量停止逻辑
      Message.info('批量停止功能开发中')
    }

    const batchDelete = async () => {
      try {
        await MessageBox.confirm(
          `确定要删除选中的 ${selectedStrategies.value.length} 个策略吗？`,
          '批量删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        // 批量删除逻辑
        const deletePromises = selectedStrategies.value.map(strategy =>
          strategyApi.deleteStrategy(strategy.id)
        )

        await Promise.all(deletePromises)
        Message.success('批量删除成功')
        selectedStrategies.value = []
        loadStrategies()
        loadStats()
      } catch (error) {
        if (error !== 'cancel') {
          Message.error('批量删除失败')
        }
      }
    }

    const resetFilter = () => {
      filterForm.type = ''
      filterForm.status = ''
      filterForm.search = ''
      pagination.page = 1
      loadStrategies()
    }

    // 辅助方法
    const getTypeLabel = (type) => {
      return strategyApi.getTypeLabel(type)
    }

    const getCategoryLabel = (category) => {
      return strategyApi.getCategoryLabel(category)
    }

    const getStatusLabel = (status) => {
      return strategyApi.getStatusInfo(status).label
    }

    const getTypeTagType = (type) => {
      const typeMap = {
        'trend_following': 'primary',
        'mean_reversion': 'success',
        'momentum': 'warning',
        'arbitrage': 'info',
        'grid': 'danger',
        'custom': ''
      }
      return typeMap[type] || ''
    }

    const getStatusTagType = (status) => {
      const statusMap = {
        'created': 'info',
        'validated': 'success',
        'active': 'success',
        'inactive': 'warning',
        'error': 'danger',
        'deleted': 'info'
      }
      return statusMap[status] || 'info'
    }

    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }

    // 生命周期
    onMounted(() => {
      loadStrategies()
      loadStats()
      loadStrategyTypes()
    })

    return {
      loading,
      strategies,
      selectedStrategies,
      strategyTypes,
      stats,
      filterForm,
      pagination,
      todayCreated,
      debounceSearch,
      loadStrategies,
      createStrategy,
      viewStrategy,
      editStrategy,
      copyStrategy,
      deleteStrategy,
      handleSelectionChange,
      batchActivate,
      batchDeactivate,
      batchDelete,
      resetFilter,
      getTypeLabel,
      getCategoryLabel,
      getStatusLabel,
      getTypeTagType,
      getStatusTagType,
      formatDate
    }
  }
}
</script>

<style scoped>
.strategy-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40px;
  color: #e4e7ed;
  z-index: 1;
}

.stat-icon.active {
  color: #67c23a;
}

.stat-icon.inactive {
  color: #f56c6c;
}

.stat-icon.new {
  color: #409eff;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.strategy-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.strategy-name .name {
  font-weight: 500;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.batch-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

.batch-buttons {
  display: flex;
  gap: 10px;
}
</style>

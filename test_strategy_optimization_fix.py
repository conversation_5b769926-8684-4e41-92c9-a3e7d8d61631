#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略优化功能修复验证脚本
测试策略ID类型、API路径和数据加载问题的修复效果
"""

import requests
import json
import time

def test_strategy_optimization_fix():
    """测试策略优化功能修复效果"""
    print("=" * 60)
    print("策略优化功能修复验证测试")
    print("=" * 60)
    
    # 前端代理地址
    frontend_url = "http://localhost:8080"
    
    # 测试1: 验证策略列表API
    print("\n1. 测试策略列表API...")
    try:
        response = requests.get(f"{frontend_url}/api/v1/strategy", timeout=10)
        if response.status_code == 200:
            strategies = response.json()
            print(f"✓ 策略列表API正常，返回 {len(strategies)} 个策略")
            
            for strategy in strategies:
                print(f"  - ID: {strategy['id']} (类型: {type(strategy['id'])}), 名称: {strategy['name']}")
                
            # 选择第一个策略进行后续测试
            if strategies:
                test_strategy_id = strategies[0]['id']
                print(f"\n选择策略ID {test_strategy_id} 进行后续测试")
                return test_strategy_id
            else:
                print("✗ 没有可用的策略进行测试")
                return None
        else:
            print(f"✗ 策略列表API失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"✗ 策略列表API异常: {e}")
        return None

def test_performance_api(strategy_id):
    """测试策略绩效API"""
    print(f"\n2. 测试策略绩效API (策略ID: {strategy_id})...")
    
    frontend_url = "http://localhost:8080"
    
    try:
        # 测试绩效API
        url = f"{frontend_url}/api/v1/strategies/{strategy_id}/performance"
        params = {
            'start_date': '2025-02-26',
            'end_date': '2025-05-27'
        }
        
        response = requests.get(url, params=params, timeout=10)
        if response.status_code == 200:
            performance_data = response.json()
            print(f"✓ 策略绩效API正常")
            print(f"  策略名称: {performance_data.get('name', 'N/A')}")
            print(f"  年化收益率: {performance_data.get('metrics', {}).get('annualized_return', 'N/A')}%")
            print(f"  夏普比率: {performance_data.get('metrics', {}).get('sharpe_ratio', 'N/A')}")
            print(f"  最大回撤: {performance_data.get('metrics', {}).get('max_drawdown', 'N/A')}%")
            print(f"  月度收益数据: {len(performance_data.get('monthly_returns', []))} 个月")
            return True
        else:
            print(f"✗ 策略绩效API失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"✗ 策略绩效API异常: {e}")
        return False

def test_optimization_api(strategy_id):
    """测试策略优化建议API"""
    print(f"\n3. 测试策略优化建议API (策略ID: {strategy_id})...")
    
    frontend_url = "http://localhost:8080"
    
    try:
        url = f"{frontend_url}/api/v1/strategies/{strategy_id}/optimization"
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            optimization_data = response.json()
            print(f"✓ 策略优化建议API正常")
            print(f"  策略名称: {optimization_data.get('name', 'N/A')}")
            print(f"  当前年化收益率: {optimization_data.get('current_performance', {}).get('annualized_return', 'N/A')}%")
            print(f"  优化建议数量: {len(optimization_data.get('optimization_suggestions', []))}")
            
            suggestions = optimization_data.get('optimization_suggestions', [])
            for i, suggestion in enumerate(suggestions, 1):
                print(f"    建议{i}: {suggestion.get('title', 'N/A')} (优先级: {suggestion.get('priority', 'N/A')})")
            
            improvements = optimization_data.get('potential_improvements', {})
            print(f"  潜在收益提升: {improvements.get('return_increase', 'N/A')}%")
            print(f"  风险降低: {improvements.get('risk_reduction', 'N/A')}%")
            
            return True
        else:
            print(f"✗ 策略优化建议API失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"✗ 策略优化建议API异常: {e}")
        return False

def test_frontend_access():
    """测试前端页面访问"""
    print(f"\n4. 测试前端页面访问...")
    
    try:
        response = requests.get("http://localhost:8080", timeout=10)
        if response.status_code == 200:
            print("✓ 前端页面访问正常")
            return True
        else:
            print(f"✗ 前端页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 前端页面访问异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始策略优化功能修复验证...")
    
    # 等待服务启动
    time.sleep(2)
    
    # 测试策略列表并获取测试策略ID
    strategy_id = test_strategy_optimization_fix()
    
    if strategy_id is None:
        print("\n❌ 无法获取测试策略，终止测试")
        return False
    
    # 测试绩效API
    performance_ok = test_performance_api(strategy_id)
    
    # 测试优化建议API
    optimization_ok = test_optimization_api(strategy_id)
    
    # 测试前端访问
    frontend_ok = test_frontend_access()
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    results = [
        ("策略列表API", strategy_id is not None),
        ("策略绩效API", performance_ok),
        ("策略优化建议API", optimization_ok),
        ("前端页面访问", frontend_ok)
    ]
    
    all_passed = True
    for test_name, passed in results:
        status = "✓ 通过" if passed else "✗ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！策略优化功能修复成功！")
        print("✅ 策略ID类型问题已修复")
        print("✅ API路径问题已修复")
        print("✅ 前端代理功能正常")
        print("✅ 数据加载功能正常")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    print("=" * 60)
    
    return all_passed

if __name__ == "__main__":
    main()

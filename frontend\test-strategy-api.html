<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            color: #333;
            margin-bottom: 10px;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>策略API测试</h1>
        
        <div class="test-section">
            <h3 class="test-title">1. 直接API调用测试</h3>
            <button onclick="testDirectAPI()">测试直接API调用</button>
            <div id="direct-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3 class="test-title">2. 模拟前端request.js处理</h3>
            <button onclick="testRequestProcessing()">测试request.js处理逻辑</button>
            <div id="request-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3 class="test-title">3. 模拟StrategyOptimization.vue数据处理</h3>
            <button onclick="testStrategyOptimization()">测试策略优化页面数据处理</button>
            <div id="strategy-result" class="result"></div>
        </div>
    </div>

    <script>
        // 1. 直接API调用测试
        async function testDirectAPI() {
            const resultDiv = document.getElementById('direct-result');
            try {
                const response = await fetch('http://localhost:8000/api/v1/strategies', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                const data = await response.json();
                resultDiv.innerHTML = `<span class="success">✅ 成功</span>\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 失败</span>\n${error.message}`;
            }
        }

        // 2. 模拟request.js的响应拦截器处理
        function simulateRequestInterceptor(response) {
            // 模拟request.js中的逻辑：如果响应包含data字段，则返回data
            if (response.data !== undefined) {
                return response.data;
            }
            return response;
        }

        async function testRequestProcessing() {
            const resultDiv = document.getElementById('request-result');
            try {
                const response = await fetch('http://localhost:8000/api/v1/strategies', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                const originalData = await response.json();
                const processedData = simulateRequestInterceptor(originalData);
                
                resultDiv.innerHTML = `<span class="success">✅ 成功</span>\n原始数据类型: ${typeof originalData}\n处理后数据类型: ${typeof processedData}\n处理后数据:\n${JSON.stringify(processedData, null, 2)}`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 失败</span>\n${error.message}`;
            }
        }

        // 3. 模拟StrategyOptimization.vue的数据处理逻辑
        function simulateStrategyOptimizationProcessing(response) {
            console.log('API响应原始数据:', response);

            // 处理响应数据格式 - 根据实际API返回结构
            let strategiesData = [];
            
            // 由于request.js的响应拦截器会直接返回response.data
            // 所以response就是API返回的数据对象: {data: [...], success: true, message: "...", total: 8}
            if (response && response.data && Array.isArray(response.data)) {
                strategiesData = response.data;
                console.log('使用API响应格式: response.data (经过request.js处理)');
            }
            // 如果response直接是数组（备用情况）
            else if (Array.isArray(response)) {
                strategiesData = response;
                console.log('使用直接数组格式');
            }
            // 如果都不匹配，记录错误信息
            else {
                console.error('无法解析策略数据格式:', response);
                strategiesData = [];
            }

            console.log('处理后的策略数据:', strategiesData);

            // 格式化策略数据，确保ID和name字段正确
            const strategies = strategiesData.map(strategy => ({
                id: String(strategy.id), // 确保ID是字符串类型
                name: strategy.name || '未命名策略',
                description: strategy.description || '',
                status: strategy.status || 'unknown',
                type: strategy.type || 'custom',
                category: strategy.category || '',
                symbol: strategy.symbol || '',
                timeframe: strategy.timeframe || ''
            }));

            return {
                strategiesData,
                strategies,
                count: strategies.length
            };
        }

        async function testStrategyOptimization() {
            const resultDiv = document.getElementById('strategy-result');
            try {
                const response = await fetch('http://localhost:8000/api/v1/strategies', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                const originalData = await response.json();
                const processedByRequest = simulateRequestInterceptor(originalData);
                const result = simulateStrategyOptimizationProcessing(processedByRequest);
                
                resultDiv.innerHTML = `<span class="success">✅ 成功</span>\n原始策略数据数量: ${result.strategiesData.length}\n格式化后策略数量: ${result.count}\n格式化后的策略:\n${JSON.stringify(result.strategies, null, 2)}`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 失败</span>\n${error.message}`;
            }
        }
    </script>
</body>
</html>

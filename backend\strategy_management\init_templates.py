"""
初始化策略模板数据
创建内置的策略模板
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sqlite3
import json
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def read_template_file(file_path: str) -> str:
    """读取模板文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        logger.error(f"模板文件不存在: {file_path}")
        return ""
    except Exception as e:
        logger.error(f"读取模板文件失败: {file_path}, 错误: {str(e)}")
        return ""

def create_builtin_templates(conn):
    """创建内置策略模板"""
    cursor = conn.cursor()

    # 双均线交叉策略模板
    dual_ma_code = """
def initialize(context):
    context.short_period = {{ short_period | default(5) }}
    context.long_period = {{ long_period | default(20) }}
    context.position = 0

def handle_bar(context, data):
    close_prices = data['close']
    if len(close_prices) < context.long_period:
        return {'action': 'HOLD', 'reason': '数据不足'}

    short_ma = close_prices.rolling(context.short_period).mean()
    long_ma = close_prices.rolling(context.long_period).mean()

    current_short = short_ma.iloc[-1]
    current_long = long_ma.iloc[-1]
    prev_short = short_ma.iloc[-2]
    prev_long = long_ma.iloc[-2]

    # 金叉买入
    if prev_short <= prev_long and current_short > current_long and context.position <= 0:
        context.position = 1
        return {
            'action': 'BUY',
            'price': close_prices.iloc[-1],
            'reason': '双均线金叉买入'
        }

    # 死叉卖出
    if prev_short >= prev_long and current_short < current_long and context.position >= 0:
        context.position = -1
        return {
            'action': 'SELL',
            'price': close_prices.iloc[-1],
            'reason': '双均线死叉卖出'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("双均线交叉策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "双均线交叉策略",
            "trend_following",
            "dual_ma_cross",
            "基于快慢双均线交叉的趋势跟踪策略，当短期均线上穿长期均线时买入，下穿时卖出",
            dual_ma_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "short_period",
                        "type": "integer",
                        "default": 5,
                        "min": 1,
                        "max": 50,
                        "description": "短期均线周期",
                        "required": True
                    },
                    {
                        "name": "long_period",
                        "type": "integer",
                        "default": 20,
                        "min": 2,
                        "max": 200,
                        "description": "长期均线周期",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "short_period": 5,
                "long_period": 20,
                "price_type": "close"
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建双均线交叉策略模板")
    else:
        logger.info("双均线交叉策略模板已存在")

    # RSI超买超卖策略模板
    rsi_code = """
def initialize(context):
    context.rsi_period = {{ rsi_period | default(14) }}
    context.overbought_level = {{ overbought_level | default(70.0) }}
    context.oversold_level = {{ oversold_level | default(30.0) }}
    context.position = 0

def calculate_rsi(prices, period=14):
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def handle_bar(context, data):
    close_prices = data['close']
    if len(close_prices) < context.rsi_period + 1:
        return {'action': 'HOLD', 'reason': '数据不足'}

    rsi = calculate_rsi(close_prices, context.rsi_period)
    current_rsi = rsi.iloc[-1]

    # RSI超卖买入
    if current_rsi < context.oversold_level and context.position <= 0:
        context.position = 1
        return {
            'action': 'BUY',
            'price': close_prices.iloc[-1],
            'reason': f'RSI超卖买入 (RSI: {current_rsi:.2f})'
        }

    # RSI超买卖出
    if current_rsi > context.overbought_level and context.position >= 0:
        context.position = -1
        return {
            'action': 'SELL',
            'price': close_prices.iloc[-1],
            'reason': f'RSI超买卖出 (RSI: {current_rsi:.2f})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("RSI超买超卖策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "RSI超买超卖策略",
            "mean_reversion",
            "rsi_oversold",
            "基于RSI指标的均值回归策略，在超卖时买入，超买时卖出",
            rsi_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "rsi_period",
                        "type": "integer",
                        "default": 14,
                        "min": 2,
                        "max": 50,
                        "description": "RSI计算周期",
                        "required": True
                    },
                    {
                        "name": "overbought_level",
                        "type": "float",
                        "default": 70.0,
                        "min": 50.0,
                        "max": 90.0,
                        "description": "超买水平",
                        "required": True
                    },
                    {
                        "name": "oversold_level",
                        "type": "float",
                        "default": 30.0,
                        "min": 10.0,
                        "max": 50.0,
                        "description": "超卖水平",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "rsi_period": 14,
                "overbought_level": 70.0,
                "oversold_level": 30.0
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建RSI超买超卖策略模板")
    else:
        logger.info("RSI超买超卖策略模板已存在")

    # 三均线策略模板
    triple_ma_code = """
def initialize(context):
    context.short_period = {{ short_period | default(5) }}
    context.medium_period = {{ medium_period | default(10) }}
    context.long_period = {{ long_period | default(20) }}
    context.position = 0

def handle_bar(context, data):
    close_prices = data['close']
    max_period = max(context.short_period, context.medium_period, context.long_period)

    if len(close_prices) < max_period + 1:
        return {'action': 'HOLD', 'reason': '数据不足'}

    # 计算三条均线
    short_ma = close_prices.rolling(context.short_period).mean().iloc[-1]
    medium_ma = close_prices.rolling(context.medium_period).mean().iloc[-1]
    long_ma = close_prices.rolling(context.long_period).mean().iloc[-1]
    current_price = close_prices.iloc[-1]

    # 多头排列：短期>中期>长期均线，且价格在短期均线之上
    if (short_ma > medium_ma > long_ma and
        current_price > short_ma and
        context.position <= 0):
        context.position = 1
        return {
            'action': 'BUY',
            'price': current_price,
            'reason': f'三均线多头排列买入 (短:{short_ma:.2f} 中:{medium_ma:.2f} 长:{long_ma:.2f})'
        }

    # 空头排列：短期<中期<长期均线，且价格在短期均线之下
    elif (short_ma < medium_ma < long_ma and
          current_price < short_ma and
          context.position >= 0):
        context.position = -1
        return {
            'action': 'SELL',
            'price': current_price,
            'reason': f'三均线空头排列卖出 (短:{short_ma:.2f} 中:{medium_ma:.2f} 长:{long_ma:.2f})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("三均线策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "三均线策略",
            "trend_following",
            "triple_ma",
            "基于三条均线排列的趋势跟踪策略，多头排列时买入，空头排列时卖出",
            triple_ma_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "short_period",
                        "type": "integer",
                        "default": 5,
                        "min": 1,
                        "max": 20,
                        "description": "短期均线周期",
                        "required": True
                    },
                    {
                        "name": "medium_period",
                        "type": "integer",
                        "default": 10,
                        "min": 5,
                        "max": 50,
                        "description": "中期均线周期",
                        "required": True
                    },
                    {
                        "name": "long_period",
                        "type": "integer",
                        "default": 20,
                        "min": 10,
                        "max": 100,
                        "description": "长期均线周期",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "short_period": 5,
                "medium_period": 10,
                "long_period": 20
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建三均线策略模板")
    else:
        logger.info("三均线策略模板已存在")

    # MACD策略模板
    macd_code = """
def initialize(context):
    context.fast_period = {{ fast_period | default(12) }}
    context.slow_period = {{ slow_period | default(26) }}
    context.signal_period = {{ signal_period | default(9) }}
    context.position = 0

def calculate_macd(prices, fast=12, slow=26, signal=9):
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram

def handle_bar(context, data):
    close_prices = data['close']
    min_periods = max(context.fast_period, context.slow_period, context.signal_period) + 10

    if len(close_prices) < min_periods:
        return {'action': 'HOLD', 'reason': '数据不足'}

    macd_line, signal_line, histogram = calculate_macd(
        close_prices, context.fast_period, context.slow_period, context.signal_period
    )

    current_macd = macd_line.iloc[-1]
    current_signal = signal_line.iloc[-1]
    prev_macd = macd_line.iloc[-2]
    prev_signal = signal_line.iloc[-2]
    current_price = close_prices.iloc[-1]

    # MACD金叉买入
    if (prev_macd <= prev_signal and current_macd > current_signal and
        context.position <= 0):
        context.position = 1
        return {
            'action': 'BUY',
            'price': current_price,
            'reason': f'MACD金叉买入 (MACD:{current_macd:.4f} Signal:{current_signal:.4f})'
        }

    # MACD死叉卖出
    elif (prev_macd >= prev_signal and current_macd < current_signal and
          context.position >= 0):
        context.position = -1
        return {
            'action': 'SELL',
            'price': current_price,
            'reason': f'MACD死叉卖出 (MACD:{current_macd:.4f} Signal:{current_signal:.4f})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("MACD策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "MACD策略",
            "trend_following",
            "macd_cross",
            "基于MACD指标的趋势跟踪策略，金叉时买入，死叉时卖出",
            macd_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "fast_period",
                        "type": "integer",
                        "default": 12,
                        "min": 5,
                        "max": 30,
                        "description": "快速EMA周期",
                        "required": True
                    },
                    {
                        "name": "slow_period",
                        "type": "integer",
                        "default": 26,
                        "min": 15,
                        "max": 50,
                        "description": "慢速EMA周期",
                        "required": True
                    },
                    {
                        "name": "signal_period",
                        "type": "integer",
                        "default": 9,
                        "min": 5,
                        "max": 20,
                        "description": "信号线周期",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "fast_period": 12,
                "slow_period": 26,
                "signal_period": 9
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建MACD策略模板")
    else:
        logger.info("MACD策略模板已存在")

    # 布林带策略模板
    bollinger_code = """
def initialize(context):
    context.period = {{ period | default(20) }}
    context.std_dev = {{ std_dev | default(2.0) }}
    context.position = 0

def calculate_bollinger_bands(prices, period=20, std_dev=2.0):
    sma = prices.rolling(period).mean()
    std = prices.rolling(period).std()
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    return upper_band, sma, lower_band

def handle_bar(context, data):
    close_prices = data['close']

    if len(close_prices) < context.period + 1:
        return {'action': 'HOLD', 'reason': '数据不足'}

    upper_band, middle_band, lower_band = calculate_bollinger_bands(
        close_prices, context.period, context.std_dev
    )

    current_price = close_prices.iloc[-1]
    current_upper = upper_band.iloc[-1]
    current_lower = lower_band.iloc[-1]
    current_middle = middle_band.iloc[-1]

    # 价格触及下轨买入
    if current_price <= current_lower and context.position <= 0:
        context.position = 1
        return {
            'action': 'BUY',
            'price': current_price,
            'reason': f'布林带下轨买入 (价格:{current_price:.2f} 下轨:{current_lower:.2f})'
        }

    # 价格触及上轨卖出
    elif current_price >= current_upper and context.position >= 0:
        context.position = -1
        return {
            'action': 'SELL',
            'price': current_price,
            'reason': f'布林带上轨卖出 (价格:{current_price:.2f} 上轨:{current_upper:.2f})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("布林带策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "布林带策略",
            "mean_reversion",
            "bollinger_bands",
            "基于布林带的均值回归策略，价格触及下轨时买入，触及上轨时卖出",
            bollinger_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "period",
                        "type": "integer",
                        "default": 20,
                        "min": 10,
                        "max": 50,
                        "description": "布林带周期",
                        "required": True
                    },
                    {
                        "name": "std_dev",
                        "type": "float",
                        "default": 2.0,
                        "min": 1.0,
                        "max": 3.0,
                        "description": "标准差倍数",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "period": 20,
                "std_dev": 2.0
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建布林带策略模板")
    else:
        logger.info("布林带策略模板已存在")

    # 布林带突破策略模板 (趋势跟踪类型)
    bollinger_breakout_code = """
def initialize(context):
    context.period = {{ period | default(20) }}
    context.std_dev = {{ std_dev | default(2.0) }}
    context.breakout_threshold = {{ breakout_threshold | default(0.02) }}
    context.position = 0

def calculate_bollinger_bands(prices, period=20, std_dev=2.0):
    sma = prices.rolling(period).mean()
    std = prices.rolling(period).std()
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    return upper_band, sma, lower_band

def handle_bar(context, data):
    close_prices = data['close']

    if len(close_prices) < context.period + 1:
        return {'action': 'HOLD', 'reason': '数据不足'}

    upper_band, middle_band, lower_band = calculate_bollinger_bands(
        close_prices, context.period, context.std_dev
    )

    current_price = close_prices.iloc[-1]
    current_upper = upper_band.iloc[-1]
    current_lower = lower_band.iloc[-1]
    prev_price = close_prices.iloc[-2]

    # 向上突破上轨买入
    if (prev_price <= upper_band.iloc[-2] and
        current_price > current_upper * (1 + context.breakout_threshold) and
        context.position <= 0):
        context.position = 1
        return {
            'action': 'BUY',
            'price': current_price,
            'reason': f'布林带向上突破买入 (价格:{current_price:.2f} 上轨:{current_upper:.2f})'
        }

    # 向下突破下轨卖出
    elif (prev_price >= lower_band.iloc[-2] and
          current_price < current_lower * (1 - context.breakout_threshold) and
          context.position >= 0):
        context.position = -1
        return {
            'action': 'SELL',
            'price': current_price,
            'reason': f'布林带向下突破卖出 (价格:{current_price:.2f} 下轨:{current_lower:.2f})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("布林带突破策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "布林带突破策略",
            "trend_following",
            "bollinger_breakout",
            "基于布林带突破的趋势跟踪策略，价格突破上轨时买入，突破下轨时卖出",
            bollinger_breakout_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "period",
                        "type": "integer",
                        "default": 20,
                        "min": 10,
                        "max": 50,
                        "description": "布林带周期",
                        "required": True
                    },
                    {
                        "name": "std_dev",
                        "type": "float",
                        "default": 2.0,
                        "min": 1.0,
                        "max": 3.0,
                        "description": "标准差倍数",
                        "required": True
                    },
                    {
                        "name": "breakout_threshold",
                        "type": "float",
                        "default": 0.02,
                        "min": 0.01,
                        "max": 0.1,
                        "description": "突破阈值",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "period": 20,
                "std_dev": 2.0,
                "breakout_threshold": 0.02
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建布林带突破策略模板")
    else:
        logger.info("布林带突破策略模板已存在")

    # 支撑阻力策略模板 (均值回归类型)
    support_resistance_code = """
def initialize(context):
    context.lookback_period = {{ lookback_period | default(20) }}
    context.support_threshold = {{ support_threshold | default(0.02) }}
    context.resistance_threshold = {{ resistance_threshold | default(0.02) }}
    context.position = 0

def find_support_resistance(prices, period=20):
    highs = prices.rolling(period).max()
    lows = prices.rolling(period).min()
    return highs.iloc[-1], lows.iloc[-1]

def handle_bar(context, data):
    close_prices = data['close']

    if len(close_prices) < context.lookback_period + 1:
        return {'action': 'HOLD', 'reason': '数据不足'}

    resistance, support = find_support_resistance(close_prices, context.lookback_period)
    current_price = close_prices.iloc[-1]

    # 接近支撑位买入
    if (current_price <= support * (1 + context.support_threshold) and
        context.position <= 0):
        context.position = 1
        return {
            'action': 'BUY',
            'price': current_price,
            'reason': f'接近支撑位买入 (价格:{current_price:.2f} 支撑:{support:.2f})'
        }

    # 接近阻力位卖出
    elif (current_price >= resistance * (1 - context.resistance_threshold) and
          context.position >= 0):
        context.position = -1
        return {
            'action': 'SELL',
            'price': current_price,
            'reason': f'接近阻力位卖出 (价格:{current_price:.2f} 阻力:{resistance:.2f})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("支撑阻力策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "支撑阻力策略",
            "mean_reversion",
            "support_resistance",
            "基于支撑阻力位的均值回归策略，价格接近支撑位时买入，接近阻力位时卖出",
            support_resistance_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "lookback_period",
                        "type": "integer",
                        "default": 20,
                        "min": 10,
                        "max": 50,
                        "description": "回看周期",
                        "required": True
                    },
                    {
                        "name": "support_threshold",
                        "type": "float",
                        "default": 0.02,
                        "min": 0.01,
                        "max": 0.05,
                        "description": "支撑位阈值",
                        "required": True
                    },
                    {
                        "name": "resistance_threshold",
                        "type": "float",
                        "default": 0.02,
                        "min": 0.01,
                        "max": 0.05,
                        "description": "阻力位阈值",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "lookback_period": 20,
                "support_threshold": 0.02,
                "resistance_threshold": 0.02
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建支撑阻力策略模板")
    else:
        logger.info("支撑阻力策略模板已存在")

    # 价格动量策略模板 (动量策略类型)
    price_momentum_code = """
def initialize(context):
    context.momentum_period = {{ momentum_period | default(10) }}
    context.momentum_threshold = {{ momentum_threshold | default(0.05) }}
    context.position = 0

def calculate_momentum(prices, period=10):
    return (prices.iloc[-1] - prices.iloc[-period-1]) / prices.iloc[-period-1]

def handle_bar(context, data):
    close_prices = data['close']

    if len(close_prices) < context.momentum_period + 1:
        return {'action': 'HOLD', 'reason': '数据不足'}

    momentum = calculate_momentum(close_prices, context.momentum_period)
    current_price = close_prices.iloc[-1]

    # 正动量买入
    if momentum > context.momentum_threshold and context.position <= 0:
        context.position = 1
        return {
            'action': 'BUY',
            'price': current_price,
            'reason': f'正动量买入 (动量:{momentum:.4f})'
        }

    # 负动量卖出
    elif momentum < -context.momentum_threshold and context.position >= 0:
        context.position = -1
        return {
            'action': 'SELL',
            'price': current_price,
            'reason': f'负动量卖出 (动量:{momentum:.4f})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("价格动量策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "价格动量策略",
            "momentum",
            "price_momentum",
            "基于价格动量的策略，正动量时买入，负动量时卖出",
            price_momentum_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "momentum_period",
                        "type": "integer",
                        "default": 10,
                        "min": 5,
                        "max": 30,
                        "description": "动量计算周期",
                        "required": True
                    },
                    {
                        "name": "momentum_threshold",
                        "type": "float",
                        "default": 0.05,
                        "min": 0.01,
                        "max": 0.2,
                        "description": "动量阈值",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "momentum_period": 10,
                "momentum_threshold": 0.05
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建价格动量策略模板")
    else:
        logger.info("价格动量策略模板已存在")

    # 成交量动量策略模板 (动量策略类型)
    volume_momentum_code = """
def initialize(context):
    context.volume_period = {{ volume_period | default(10) }}
    context.volume_threshold = {{ volume_threshold | default(1.5) }}
    context.price_change_threshold = {{ price_change_threshold | default(0.02) }}
    context.position = 0

def calculate_volume_momentum(volumes, prices, period=10):
    avg_volume = volumes.rolling(period).mean().iloc[-2]
    current_volume = volumes.iloc[-1]
    price_change = (prices.iloc[-1] - prices.iloc[-2]) / prices.iloc[-2]
    return current_volume / avg_volume, price_change

def handle_bar(context, data):
    close_prices = data['close']
    volumes = data['volume']

    if len(close_prices) < context.volume_period + 1:
        return {'action': 'HOLD', 'reason': '数据不足'}

    volume_ratio, price_change = calculate_volume_momentum(
        volumes, close_prices, context.volume_period
    )
    current_price = close_prices.iloc[-1]

    # 成交量放大且价格上涨买入
    if (volume_ratio > context.volume_threshold and
        price_change > context.price_change_threshold and
        context.position <= 0):
        context.position = 1
        return {
            'action': 'BUY',
            'price': current_price,
            'reason': f'成交量动量买入 (量比:{volume_ratio:.2f} 涨幅:{price_change:.4f})'
        }

    # 成交量放大且价格下跌卖出
    elif (volume_ratio > context.volume_threshold and
          price_change < -context.price_change_threshold and
          context.position >= 0):
        context.position = -1
        return {
            'action': 'SELL',
            'price': current_price,
            'reason': f'成交量动量卖出 (量比:{volume_ratio:.2f} 跌幅:{price_change:.4f})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("成交量动量策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "成交量动量策略",
            "momentum",
            "volume_momentum",
            "基于成交量动量的策略，成交量放大配合价格变动时进行交易",
            volume_momentum_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "volume_period",
                        "type": "integer",
                        "default": 10,
                        "min": 5,
                        "max": 30,
                        "description": "成交量计算周期",
                        "required": True
                    },
                    {
                        "name": "volume_threshold",
                        "type": "float",
                        "default": 1.5,
                        "min": 1.2,
                        "max": 3.0,
                        "description": "成交量放大倍数",
                        "required": True
                    },
                    {
                        "name": "price_change_threshold",
                        "type": "float",
                        "default": 0.02,
                        "min": 0.01,
                        "max": 0.1,
                        "description": "价格变动阈值",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "volume_period": 10,
                "volume_threshold": 1.5,
                "price_change_threshold": 0.02
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建成交量动量策略模板")
    else:
        logger.info("成交量动量策略模板已存在")

    logger.info("策略模板创建完成")

def main():
    """主函数"""
    logger.info("开始初始化策略模板...")

    # 获取数据库路径
    db_paths = [
        'app.db',
        'backend/app.db',
        'app/database.db',
        'app/db/database.db'
    ]

    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break

    if not db_path:
        db_path = 'app.db'  # 使用默认路径

    logger.info(f"使用数据库: {db_path}")

    # 连接数据库
    conn = sqlite3.connect(db_path)

    try:
        # 创建内置模板
        create_builtin_templates(conn)

        # 提交更改
        conn.commit()
        logger.info("策略模板初始化完成")

    except Exception as e:
        conn.rollback()
        logger.error(f"初始化策略模板失败: {str(e)}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    main()

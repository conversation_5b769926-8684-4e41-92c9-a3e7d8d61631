"""
初始化策略模板数据
创建内置的策略模板
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sqlite3
import json
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def read_template_file(file_path: str) -> str:
    """读取模板文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        logger.error(f"模板文件不存在: {file_path}")
        return ""
    except Exception as e:
        logger.error(f"读取模板文件失败: {file_path}, 错误: {str(e)}")
        return ""

def create_builtin_templates(conn):
    """创建内置策略模板"""
    cursor = conn.cursor()

    # 双均线交叉策略模板
    dual_ma_code = """
def initialize(context):
    context.short_period = {{ short_period | default(5) }}
    context.long_period = {{ long_period | default(20) }}
    context.position = 0

def handle_bar(context, data):
    close_prices = data['close']
    if len(close_prices) < context.long_period:
        return {'action': 'HOLD', 'reason': '数据不足'}

    short_ma = close_prices.rolling(context.short_period).mean()
    long_ma = close_prices.rolling(context.long_period).mean()

    current_short = short_ma.iloc[-1]
    current_long = long_ma.iloc[-1]
    prev_short = short_ma.iloc[-2]
    prev_long = long_ma.iloc[-2]

    # 金叉买入
    if prev_short <= prev_long and current_short > current_long and context.position <= 0:
        context.position = 1
        return {
            'action': 'BUY',
            'price': close_prices.iloc[-1],
            'reason': '双均线金叉买入'
        }

    # 死叉卖出
    if prev_short >= prev_long and current_short < current_long and context.position >= 0:
        context.position = -1
        return {
            'action': 'SELL',
            'price': close_prices.iloc[-1],
            'reason': '双均线死叉卖出'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("双均线交叉策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "双均线交叉策略",
            "trend_following",
            "dual_ma_cross",
            "基于快慢双均线交叉的趋势跟踪策略，当短期均线上穿长期均线时买入，下穿时卖出",
            dual_ma_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "short_period",
                        "type": "integer",
                        "default": 5,
                        "min": 1,
                        "max": 50,
                        "description": "短期均线周期",
                        "required": True
                    },
                    {
                        "name": "long_period",
                        "type": "integer",
                        "default": 20,
                        "min": 2,
                        "max": 200,
                        "description": "长期均线周期",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "short_period": 5,
                "long_period": 20,
                "price_type": "close"
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建双均线交叉策略模板")
    else:
        logger.info("双均线交叉策略模板已存在")

    # RSI超买超卖策略模板
    rsi_code = """
def initialize(context):
    context.rsi_period = {{ rsi_period | default(14) }}
    context.overbought_level = {{ overbought_level | default(70.0) }}
    context.oversold_level = {{ oversold_level | default(30.0) }}
    context.position = 0

def calculate_rsi(prices, period=14):
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def handle_bar(context, data):
    close_prices = data['close']
    if len(close_prices) < context.rsi_period + 1:
        return {'action': 'HOLD', 'reason': '数据不足'}

    rsi = calculate_rsi(close_prices, context.rsi_period)
    current_rsi = rsi.iloc[-1]

    # RSI超卖买入
    if current_rsi < context.oversold_level and context.position <= 0:
        context.position = 1
        return {
            'action': 'BUY',
            'price': close_prices.iloc[-1],
            'reason': f'RSI超卖买入 (RSI: {current_rsi:.2f})'
        }

    # RSI超买卖出
    if current_rsi > context.overbought_level and context.position >= 0:
        context.position = -1
        return {
            'action': 'SELL',
            'price': close_prices.iloc[-1],
            'reason': f'RSI超买卖出 (RSI: {current_rsi:.2f})'
        }

    return {'action': 'HOLD'}
"""

    # 检查是否已存在
    cursor.execute("SELECT id FROM strategy_templates WHERE name = ? AND is_builtin = 1",
                   ("RSI超买超卖策略",))
    if not cursor.fetchone():
        cursor.execute("""
            INSERT INTO strategy_templates (
                name, type, category, description, code_template,
                parameter_schema, default_parameters, is_builtin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "RSI超买超卖策略",
            "mean_reversion",
            "rsi_oversold",
            "基于RSI指标的均值回归策略，在超卖时买入，超买时卖出",
            rsi_code,
            json.dumps({
                "parameters": [
                    {
                        "name": "rsi_period",
                        "type": "integer",
                        "default": 14,
                        "min": 2,
                        "max": 50,
                        "description": "RSI计算周期",
                        "required": True
                    },
                    {
                        "name": "overbought_level",
                        "type": "float",
                        "default": 70.0,
                        "min": 50.0,
                        "max": 90.0,
                        "description": "超买水平",
                        "required": True
                    },
                    {
                        "name": "oversold_level",
                        "type": "float",
                        "default": 30.0,
                        "min": 10.0,
                        "max": 50.0,
                        "description": "超卖水平",
                        "required": True
                    }
                ]
            }),
            json.dumps({
                "rsi_period": 14,
                "overbought_level": 70.0,
                "oversold_level": 30.0
            }),
            1,
            datetime.now().isoformat()
        ))
        logger.info("创建RSI超买超卖策略模板")
    else:
        logger.info("RSI超买超卖策略模板已存在")

    # 简化版本，只创建基本的模板
    logger.info("策略模板创建完成")

def main():
    """主函数"""
    logger.info("开始初始化策略模板...")

    # 获取数据库路径
    db_path = 'app/database.db'
    if not os.path.exists(db_path):
        db_path = 'app/db/database.db'

    # 连接数据库
    conn = sqlite3.connect(db_path)

    try:
        # 创建内置模板
        create_builtin_templates(conn)

        # 提交更改
        conn.commit()
        logger.info("策略模板初始化完成")

    except Exception as e:
        conn.rollback()
        logger.error(f"初始化策略模板失败: {str(e)}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    main()
